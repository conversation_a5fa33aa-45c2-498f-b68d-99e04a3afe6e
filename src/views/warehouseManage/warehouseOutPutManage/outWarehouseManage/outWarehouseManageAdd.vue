<template>
  <PageContainer v-loading="pageLoading" :footer="true">
    <div slot="content" class="table-content">
      <div class="content-title">
        <span @click="$router.go(-1)">
          <i class="el-icon-arrow-left"></i>
          <span> {{ pageTitle }}</span>
        </span>
      </div>
      <div class="content_box">
        <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" label-width="120px" :rules="rules">
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            <span>单据信息</span>
          </div>
          <el-form-item label="出库单号：" prop="recordNumber">
            <el-input v-model.trim="formInline.recordNumber" maxlength="100" show-word-limit type="text" placeholder="自动生成" disabled></el-input>
          </el-form-item>
          <el-form-item label="出库类型：" prop="outwarehouseType">
            <el-select v-model.trim="formInline.outwarehouseType" filterable placeholder="出库类型">
              <el-option v-for="item in outboundTypeList" :key="item.dictionaryDetailsId" :label="item.dictionaryDetailsName" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="出库仓库：" prop="warehouseId">
            <el-select v-model.trim="formInline.warehouseId" filterable placeholder="出库仓库">
              <el-option v-for="item in warehouseList" :key="item.id" :label="item.warehouseName" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="出库日期：" prop="outwarehouseDate">
            <el-date-picker v-model="formInline.outwarehouseDate" type="date" value-format="yyyy-MM-dd" placeholder="出库日期"> </el-date-picker>
          </el-form-item>
          <el-form-item label="出库金额(元)：" prop="outwarehouseAmount">
            <el-input v-model.trim="formInline.outwarehouseAmount" maxlength="30" show-word-limit type="text" placeholder="自动生成" disabled></el-input>
          </el-form-item>
          <el-form-item label="申请人部门：" prop="applicantDepartmentId">
            <el-cascader
              v-model="formInline.applicantDepartmentId"
              placeholder="请选择部门"
              :options="deptList"
              :props="deptTree"
              :show-all-levels="false"
              clearable
              filterable
              style="width: 100%"
              @change="selectDept"
            >
            </el-cascader>
          </el-form-item>
          <el-form-item label="申请人：" prop="applicantId">
            <el-select v-model="formInline.applicantId" placeholder="请选择人员" clearable filterable style="width: 100%">
              <el-option v-for="item in personList" :key="item.id" :label="item.staffName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="关联工单：" prop="professionalCategoryCode">
            <el-input v-model.trim="formInline.workNum" type="text" placeholder="关联工单" @click.native="showWorkOrder"></el-input>
          </el-form-item>
          <el-form-item style="display: block" label="摘要信息：" prop="remarks">
            <el-input v-model.trim="formInline.remarks" maxlength="100" show-word-limit type="textarea" placeholder="请输入" class="cascaderWid"></el-input>
          </el-form-item>
          <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
            <span class="green_line"></span>
            明细信息
          </div>
          <el-button type="primary" @click="showaddConsumables">新增</el-button>
          <el-button type="primary" @click="batchImport">批量导入</el-button>
          <el-button type="danger" @click="handleBatchDelete()">批量删除</el-button>
          <div v-if="tableData.length > 0">
            <el-table :data="tableData" stripe height="330px" style="width: 100%; margin-top: 15px" @selection-change="handleSelectionChange">
              <el-table-column type="selection"> </el-table-column>
              <el-table-column type="index" label="序号"> </el-table-column>
              <el-table-column prop="materialCode" label="耗材编码" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="materialName" label="耗材名称" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="materialTypeName" label="耗材类型" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="model" label="规格型号" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="basicUnitName" label="计量单位" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="brandName" label="品牌" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="operateCount" label="出库数量" width="200">
                <template v-slot="{ row }">
                  <el-input
                    v-model.number="row.operateCount"
                    style="width: 80%"
                    placeholder="请输入数量"
                    maxlength="8"
                    @change="handleQuantityChange(row)"
                    @input="row.operateCount = Math.abs(String(row.operateCount).replace(/[^\d]/g, '')) || ''"
                    @blur="validateQuantity(row)"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="unitPriceStr" label="单价(元)" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="sumAmount" label="金额"> </el-table-column>
              <el-table-column label="操作">
                <template #default="{ row }">
                  <el-button type="text" style="color: red" @click="onOperate(row)"> 删除 </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <!-- 审核信息表单，仅在审核模式下显示 -->
          <div v-if="routeQueryType === 'review'" class="mt-20">
            <div class="toptip" style="padding: 0; border: 0; font-weight: bold">
              <span class="green_line"></span>
              <span>审核信息</span>
            </div>
            <el-form ref="auditForm" :model="form" label-width="100px" :rules="auditRules" class="audit-form">
              <el-form-item label="审核结果" prop="type">
                <el-radio v-model="form.type" label="1">通过</el-radio>
                <el-radio v-model="form.type" label="0">挂起</el-radio>
              </el-form-item>
              <br />
              <el-form-item label="审核意见" prop="opinion" class="opinion-item">
                <el-input v-model="form.opinion" maxlength="100" show-word-limit type="textarea" placeholder="请输入" style="width: 70%" />
              </el-form-item>
            </el-form>
          </div>
        </el-form>
      </div>
      <!--耗材-->
      <addConsumables
        ref="addControlRow"
        :dialogVisible="dialogVisible"
        :warehouseId="formInline.warehouseId"
        @handleConsumablesSelect="handleConsumablesSelect"
        @closeDialog="closeDialog"
      ></addConsumables>
      <!--关联工单-->
      <workOrderAdd ref="addControlRow" :workOrderVisible="workOrderVisible" @handleworkOrder="handleworkOrder" @closeWorkOrderDialog="closeWorkOrderDialog"></workOrderAdd>
      <!--批量导入弹窗-->
      <el-dialog title="批量导入" :visible.sync="dialogVisibleExport" width="30%">
        <div>
          <div style="display: flex; align-items: center; gap: 10px">
            <el-upload
              action=""
              style="display: inline-block"
              :file-list="fileList"
              :limit="1"
              :before-upload="beforeAvatarUpload"
              :http-request="(file) => httpRequset(file)"
              :on-remove="(file, fileList) => handleRemove(file, fileList)"
            >
              <span style="white-space: nowrap">上传附件：</span>
              <el-button :disabled="Boolean(fileUrl)" size="small" type="primary">点击上传</el-button>
              <span style="font-size: 14px; color: #2aa4d9; margin-left: 20px" @click.stop="exportImportTemplete">模板下载</span>
              <div style="margin-top: 15px">支持扩展名.xls/.xlsx</div>
              <div style="margin-top: 5px; color: #e6a23c; font-size: 12px">
                <i class="el-icon-warning"></i> 只能导入当前选择仓库"{{ warehouseList.find((item) => item.id === formInline.warehouseId)?.warehouseName || '' }}"中已入库的耗材
              </div>
            </el-upload>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button v-if="exportCatch != 0" @click="failureRecord">失败记录</el-button>
          <el-button @click="dialogVisibleExport = false">取 消</el-button>
          <el-button type="primary" @click="dialogVisibleExport = false">确 定</el-button>
        </span>
      </el-dialog>
    </div>
    <div slot="footer">
      <el-button type="" @click="$router.go(-1)">取消</el-button>
      <el-button v-if="routeQueryType == 'add'" type="" @click="saveForm(1)">暂存</el-button>
      <el-button v-if="routeQueryType != 'review'" type="primary" :loading="formLoading" @click="saveForm(2)">提交</el-button>
      <el-button v-if="routeQueryType == 'review'" type="primary" :loading="formLoading" @click="submitAudit">审核提交</el-button>
    </div>
  </PageContainer>
</template>
<script>
import addConsumables from '../inWarehouseManage/addConsumables.vue'
import workOrderAdd from '../inWarehouseManage/workOrderAdd.vue'
import addControlRow from '@/views/sysManagement/auth/unifiedPermissions/addControlRow.vue'
import axios from 'axios'
import moment from 'moment'
export default {
  name: 'outWarehouseManageAdd',
  components: { addControlRow, addConsumables, workOrderAdd },
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新建出库单',
        edit: '编辑出库单',
        review: '出库单审核'
      }
      to.meta.title = typeList[to.query.type] ?? '新增出库单'
    }
    next()
  },
  data() {
    return {
      formLoading: false,
      pageLoading: false,
      warehouseList: [],
      outboundTypeList: [],
      personList: [],
      fileList: [],
      dialogVisibleExport: false,
      multipleSelection: [], // 新增多选存储
      tableData: [],
      dialogVisible: false,
      workOrderVisible: false,
      routeQueryType: '',
      isRestoringWarehouse: false, // 添加标志位防止监听器无限循环
      userDepartmentProps: {
        children: 'children',
        label: 'deptName',
        value: 'id',
        checkStrictly: true,
        multiple: false
      },
      majorList: [],
      useDeptList: [],
      formInline: {
        workNum: '',
        workId: '',
        professionalCategoryCode: '',
        assetsRemarks: '', // 资产备注说明
        remarks: '' // 备注
      },
      // 审核相关数据
      form: {
        type: '1',
        opinion: ''
      },
      rules: {
        outwarehouseType: [{ required: true, message: '请选择出库类型', trigger: 'change' }],
        warehouseId: [{ required: true, message: '请选择出库仓库', trigger: 'change' }],
        outwarehouseDate: [{ required: true, message: '请选择出库日期', trigger: 'change' }],
        applicantDepartmentId: [{ required: true, message: '请选择申请人部门', trigger: 'change' }],
        applicantId: [{ required: true, message: '请选择申请人', trigger: 'change' }]
      },
      fileUrl: '',
      fileName: '',
      dataName: '',
      exportCatch: 0,
      fileCatch: {},
      userInfo: {},
      deptList: [],
      deptTree: {
        value: 'id',
        label: 'deptName',
        children: 'children',
        multiple: false,
        checkStrictly: true
      },
      auditRules: {
        type: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
        opinion: [{ required: false, message: '请输入审核意见', trigger: 'blur' }]
      }
    }
  },
  computed: {
    // 新增计算属性
    totalAmount() {
      return this.tableData
        .reduce((sum, row) => {
          return sum + (Number(row.sumAmount) || 0)
        }, 0)
        .toFixed(2)
    },
    pageTitle() {
      let title = '出库单'
      switch (this.routeQueryType) {
        case 'add':
          title = '新增' + title
          break
        case 'edit':
          title = '编辑' + title
          break
        case 'review':
          title = title + '审核'
          break
      }
      return title
    }
  },
  watch: {
    // 监听计算属性变化更新表单字段
    totalAmount(newVal) {
      this.formInline.outwarehouseAmount = newVal
    },
    // 监听出库仓库变化，清空已选择的耗材明细
    'formInline.warehouseId'(newVal, oldVal) {
      // 如果正在恢复仓库选择，跳过监听器执行
      if (this.isRestoringWarehouse) {
        return
      }

      if (oldVal && newVal !== oldVal && this.tableData.length > 0) {
        this.$confirm('更换出库仓库将清空已选择的耗材明细，是否继续？', '提示', {
          cancelButtonClass: 'el-button--primary is-plain',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.tableData = []
          })
          .catch(() => {
            // 用户取消，恢复原来的仓库选择
            this.isRestoringWarehouse = true
            this.$nextTick(() => {
              this.formInline.warehouseId = oldVal
              // 恢复完成后重置标志位
              this.$nextTick(() => {
                this.isRestoringWarehouse = false
              })
            })
          })
      }
    },
    'form.type'(newVal) {
      console.log(newVal, 'newVal')
      if (newVal === '0') {
        this.auditRules.opinion[0].required = true
      } else {
        this.auditRules.opinion[0].required = false
      }
    }
  },
  created() {
    this.userInfo = this.$store.state.user.userInfo.user
  },
  mounted() {
    this.getWarehouseList()
    this.getWarehouseType()
    this.routeQueryType = this.$route.query.type

    this.getDeptList().then(() => {
      // 部门数据加载完成后设置默认值
      this.setDefaultValues()

      // 如果是编辑模式或审核模式，在部门数据加载完成后再加载出库单数据
      if ((this.routeQueryType === 'edit' || this.routeQueryType === 'review') && this.$route.query.id) {
        this.getOutWarehouseRecordById()
      }
    })
  },
  methods: {
    // 获取出库单详情
    getOutWarehouseRecordById() {
      this.pageLoading = true
      let params = {
        id: this.$route.query.id || '',
        userName: this.userInfo.staffName,
        userId: this.userInfo.staffId,
        recordNumber: this.$route.query.recordNumber || ''
      }
      this.$api.warehouseApi.getOutWarehouseRecordById(params).then((res) => {
        try {
          if (res.code == 200) {
            const data = res.data
            // 填充表单数据 - 只保留新增时需要的字段
            // 处理申请人部门ID，获取完整的部门路径以支持级联选择器
            let applicantDepartmentPath = []
            if (data.applicantDepartmentId) {
              console.log('接口返回的申请人部门ID:', data.applicantDepartmentId)
              applicantDepartmentPath = this.findDeptPath(data.applicantDepartmentId)
              console.log('计算出的部门路径:', applicantDepartmentPath)
            }

            this.formInline = {
              // 基本信息
              recordNumber: data.recordNumber || '',
              outwarehouseType: data.outwarehouseType || '',
              warehouseId: data.warehouseId || '',
              outwarehouseDate: data.outwarehouseDate ? moment(data.outwarehouseDate).format('YYYY-MM-DD') : '',
              outwarehouseAmount: data.outwarehouseAmount || '',
              // 使用计算出的完整部门路径
              applicantDepartmentId: applicantDepartmentPath,
              applicantId: data.applicantId || '',
              workNum: data.workNum || '',
              workId: data.workId || '',
              remarks: data.remarks || ''
            }
            // 填充表格数据
            if (data.materialRecordList && data.materialRecordList.length > 0) {
              this.tableData = data.materialRecordList.map((item) => ({
                id: item.id,
                materialCode: item.materialCode,
                materialName: item.materialName,
                materialTypeName: item.materialTypeName,
                model: item.model,
                basicUnitName: item.basicUnitName,
                brandName: item.brandName || '',
                operateCount: item.operateCount || '',
                unitPrice: item.unitPrice || '',
                unitPriceStr: item.unitPriceStr || item.unitPrice || '',
                sumAmount: item.sumAmount || ''
              }))
            }
            // 加载部门人员数据
            if (this.formInline.applicantDepartmentId && this.formInline.applicantDepartmentId.length > 0) {
              this.getPersonList(this.formInline.applicantDepartmentId)
            }
          }
        } catch (error) {
          this.$message.error('获取出库单详情失败')
        } finally {
          this.pageLoading = false
        }
      })
    },
    // 提交
    saveForm(status) {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          // 检查表格数据是否存在
          if (this.tableData.length === 0) {
            this.$message.error('请添加出库物品')
            return
          }
          // 检查每行的出库数量是否已填写
          const emptyCountRows = this.tableData.filter((row) => !row.operateCount)
          if (emptyCountRows.length > 0) {
            this.$message.error('请填写所有行的出库数量')
            return
          }
          // 获取选中的仓库对象
          const selectedWarehouse = this.warehouseList.find((item) => item.id === this.formInline.warehouseId)
          // 获取申请人名称
          const selectedPerson = this.personList.find((item) => item.id === this.formInline.applicantId)
          // 获取选中的出库类型对象
          const selectedOutboundType = this.outboundTypeList.find((item) => item.id === this.formInline.outwarehouseType)
          // 获取部门名称（处理级联选择器的数组值）
          const findDeptName = (idList, list) => {
            const targetId = idList?.slice(-1)[0] // 取最后一级部门ID
            let result = ''
            const search = (arr) => {
              arr.forEach((item) => {
                if (item.id === targetId) result = item.deptName
                if (item.children) search(item.children)
              })
            }
            search(list)
            return result
          }
          this.formLoading = true
          // 构建提交参数，只包含必要的字段
          let param = {
            // 基本信息
            recordNumber: this.formInline.recordNumber || '', // 出库单号
            outwarehouseType: this.formInline.outwarehouseType || '', // 出库类型ID
            warehouseId: this.formInline.warehouseId || '', // 出库仓库ID
            outwarehouseDate: this.formInline.outwarehouseDate || '', // 出库日期
            outwarehouseAmount: this.formInline.outwarehouseAmount || '', // 出库金额(元)
            applicantDepartmentId: this.formInline.applicantDepartmentId ? this.formInline.applicantDepartmentId[this.formInline.applicantDepartmentId.length - 1] : '', // 申请人部门ID
            applicantId: this.formInline.applicantId || '', // 申请人ID
            workNum: this.formInline.workNum || '', // 关联工单号
            workId: this.formInline.workId || '', // 关联工单ID
            remarks: this.formInline.remarks || '', // 摘要信息/备注
            // 处理后的数据
            materialRecordArrayStr: JSON.stringify(this.tableData), // 出库物品明细数据(JSON字符串)
            userId: this.userInfo.staffId, // 当前操作用户ID
            userName: this.userInfo.staffName, // 当前操作用户姓名
            status: status, // 单据状态(0:暂存 1:提交)
            createSource: '0', // 创建来源(0:PC端)
            warehouseName: selectedWarehouse?.warehouseName || '', // 出库仓库名称
            outwarehouseTypeName: selectedOutboundType?.dictionaryDetailsName || '', // 出库类型名称
            applicantName: selectedPerson?.staffName || '', // 申请人姓名
            applicantDepartmentName: findDeptName(this.formInline.applicantDepartmentId, this.deptList) // 申请人部门名称
          }
          // 如果是编辑模式，确保传递id
          if (this.routeQueryType === 'edit' && this.$route.query.id) {
            param.id = this.$route.query.id
          }
          this.$api.warehouseApi
            .outwarehouseRecordSave(param)
            .then((res) => {
              this.formLoading = false
              if (res.code == '200') {
                this.$message({
                  message: status === 1 ? '暂存成功' : '保存成功',
                  type: 'success'
                })
                this.$router.go(-1)
              } else {
                this.$message({
                  message: res.message,
                  type: 'error'
                })
              }
            })
            .catch((msg) => this.$message.error(msg || (status === 1 ? '暂存失败' : '保存失败')))
        }
      })
    },
    // 获取仓库列表
    getWarehouseList() {
      let param = {
        pageSize: 99999,
        CurrentPage: 1,
        status: '0'
      }
      this.$api.warehouseApi.getWarehouseByPage(param).then((res) => {
        if (res.code == 200) {
          this.warehouseList = res.data.list
        }
      })
    },
    // 获取出库类型
    getWarehouseType() {
      const params = {
        pageSize: 99999,
        currentPage: 1,
        userType: 1,
        userId: this.userInfo.staffId,
        userName: this.userInfo.staffName,
        dictionaryCategoryId: 'outbound_type',
        dictionaryDetailsStatus: '1'
      }
      this.$api.warehouseApi.getDictByPage(params).then((res) => {
        if (res.code == '200') {
          this.outboundTypeList = res.data
        } else {
          throw res.msg || res.message
        }
      })
    },
    // 获取部门
    getDeptList() {
      return new Promise((resolve) => {
        this.$api
          .getDeptList()
          .then((res) => {
            if (res.code == 200) {
              this.allDept = res.data
              this.deptList = this.transformDeptData(res.data, 'id', 'pid', 'children')
            }
            resolve()
          })
          .catch(() => {
            resolve() // 即使失败也要resolve
          })
      })
    },
    // 部门数据转换为树形结构
    transformDeptData(data, id, pid, children) {
      if (!data || !Array.isArray(data) || data.length === 0) {
        console.log('部门数据为空或格式不正确')
        return []
      }
      const res = []
      const temp = {}
      try {
        // 第一次遍历，建立id到节点的映射
        data.forEach((item) => {
          if (item && item[id]) {
            temp[item[id]] = { ...item }
          }
        })
        // 第二次遍历，建立父子关系
        data.forEach((item) => {
          if (!item) return
          const itemId = item[id]
          const tempPid = item[pid]
          // 检查是否有有效的父ID，且父ID存在于数据中
          if (tempPid && tempPid !== '0' && tempPid !== itemId && temp[tempPid]) {
            if (!temp[tempPid][children]) {
              temp[tempPid][children] = []
            }
            temp[tempPid][children].push(temp[itemId])
          } else {
            // 根节点或父节点不存在的情况
            if (temp[itemId]) {
              res.push(temp[itemId])
            }
          }
        })
        return res
      } catch (error) {
        return []
      }
    },
    selectDept(val) {
      console.log('选择的部门值:', val)
      const deptArr = []
      if (val && val.length > 0) {
        // 现在是单选，直接取最后一个ID（叶子节点）
        const leafId = val[val.length - 1]
        deptArr.push(leafId)
      }
      console.log('最终选择的部门IDs:', deptArr)
      if (deptArr.length > 0) {
        this.getPersonList(deptArr)
      } else {
        this.personList = []
        this.formInline.applicantId = []
      }
    },
    // 获取人员
    getPersonList(deptIds) {
      return new Promise((resolve) => {
        // 创建请求参数
        let params = {
          current: 1,
          size: 9999,
          sex: '',
          pmId: '',
          postId: '', // 岗位
          stationStatus: ''
        }
        // 如果传入了部门ID参数
        if (deptIds && Array.isArray(deptIds) && deptIds.length > 0) {
          params.officeId = deptIds.join(',')
        }
        this.$api
          .staffList(params)
          .then((res) => {
            if (res.code == 200) {
              this.personList = res.data.records
              if (this.personList.length == 0) {
                this.$set(this.formInline, 'applicantId', '')
              } else {
                // 检查applicantId是否存在且是数组
                if (this.formInline.applicantId && Array.isArray(this.formInline.applicantId) && this.formInline.applicantId.length > 0) {
                  // 筛选出在当前人员列表中存在的ID
                  const filteredIds = []
                  this.formInline.applicantId.forEach((i) => {
                    this.personList.forEach((j) => {
                      if (i == j.id) {
                        filteredIds.push(i)
                      }
                    })
                  })
                  // 使用$set确保响应式更新
                  this.$set(this.formInline, 'applicantId', filteredIds.length > 0 ? filteredIds[0] : '')
                } else if (this.formInline.applicantId && !Array.isArray(this.formInline.applicantId)) {
                  // 如果applicantId存在但不是数组，检查是否在当前人员列表中
                  const exists = this.personList.some((person) => person.id === this.formInline.applicantId)
                  if (!exists) {
                    this.$set(this.formInline, 'applicantId', '')
                  }
                }
              }
            }
            resolve()
          })
          .catch(() => {
            resolve() // 即使失败也要resolve
          })
      })
    },
    // 打开关联工单
    showWorkOrder() {
      this.workOrderVisible = true
    },
    beforeAvatarUpload(file) {
      // 验证文件类型
      const extension = file.name.split('.').pop().toLowerCase()
      const isValidType = ['xls', 'xlsx'].includes(extension)
      if (!isValidType) {
        this.$message.error('只能上传.xls或.xlsx格式文件')
        return false
      }
      return true
    },
    httpRequset(file) {
      const params = new FormData()
      params.append('file', file.file)
      // 从store中获取用户信息，如果store中没有则从LOGINDATA中获取
      let hospitalCode, unitCode
      if (this.$store.getters['user/isLogin']) {
        const userInfo = this.$store.state.user.userInfo.user
        hospitalCode = userInfo.hospitalCode ?? 'BJSJTYY'
        unitCode = userInfo.unitCode ?? 'BJSYGJ'
      } else {
        const loginData = JSON.parse(sessionStorage.getItem('LOGINDATA') || '{}')
        hospitalCode = loginData.hospitalCode || 'BJSJTYY'
        unitCode = loginData.unitCode || 'BJSYGJ'
      }
      params.append('hospitalCode', hospitalCode)
      params.append('unitCode', unitCode)
      axios({
        method: 'post',
        url: __PATH.BASE_URL_HSC + 'outwarehouseRecord/importConsumableExcelCheck',
        data: params,
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      })
        .then((res) => {
          if (res.code == 200) {
            this.fileUrl = res.data.fileKey
            this.fileName = res.data.name
            this.fileList = [
              {
                name: res.data.name,
                url: res.data.fileKey
              }
            ]
            if (!this.dataName) {
              this.dataName = res.data.name.replace(/\.[^/.]+$/, '')
            }
            this.$message({
              message: '上传成功',
              type: 'success'
            })
          } else {
            const failNumber = +res.data.msg.match(/导入失败(\d+)条/)[1]
            this.exportCatch = failNumber
            this.fileCatch = file
            this.fileList = []
            failNumber > 0 ? this.$message.error(res.data.msg) : this.$message.success(res.data.msg)
            this.tableData = this.tableData.concat(res.data.data.list)
          }
        })
        .catch((err) => {
          this.fileList = []
          this.$message({
            message: '上传失败',
            type: 'error'
          })
        })
    },
    // 下载失败记录
    failureRecord() {
      // 从store中获取用户信息，如果store中没有则从LOGINDATA中获取
      let hospitalCode, unitCode
      if (this.$store.getters['user/isLogin']) {
        const userInfo = this.$store.state.user.userInfo.user
        hospitalCode = userInfo.hospitalCode ?? 'BJSJTYY'
        unitCode = userInfo.unitCode ?? 'BJSYGJ'
      } else {
        const baseInfo = JSON.parse(sessionStorage.getItem('LOGINDATA') || '{}')
        hospitalCode = baseInfo.hospitalCode || 'BJSJTYY'
        unitCode = baseInfo.unitCode || 'BJSYGJ'
      }
      const params = new FormData()
      params.append('file', this.fileCatch.file)
      // 将单位参数也加入FormData
      params.append('unitCode', unitCode)
      params.append('hospitalCode', hospitalCode)
      axios({
        method: 'post',
        url: __PATH.BASE_URL_HSC + 'outwarehouseRecord/importConsumableExcelDownLoad',
        data: params,
        responseType: 'arraybuffer',
        headers: {
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      }).then((res) => {
        if (res.status == 200 && !res.data.code) {
          let name = '导入失败记录.xls'
          let blob = new Blob([res.data])
          let url = URL.createObjectURL(blob)
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          window.URL.revokeObjectURL(url)
          if (res.data.code == 500) {
            this.$message.error(res.data.message)
          } else if (res.status == 200 && !res.data.code) {
            this.$message.success('下载成功')
            this.exportCatch = 0
          } else {
            this.$message.error('下载失败')
          }
        }
      })
    },
    handleRemove(file, fileList) {
      this.fileUrl = ''
      this.fileList = []
    },
    // 批量导入
    batchImport() {
      if (!this.formInline.warehouseId) {
        this.$message.warning('请先选择出库仓库')
        return
      }
      this.dialogVisibleExport = true
    },
    // 处理多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 耗材数据
    handleConsumablesSelect(selectedData) {
      this.dialogVisible = false
      this.tableData = this.tableData.concat(
        selectedData.map((item) => ({
          ...item,
          operateCount: '', // 初始化出库数量为0
          sumAmount: '' // 初始化出库数量为0
        }))
      )
    },
    // 关联工单数据
    handleworkOrder(data) {
      this.formInline.workNum = data[0].workNum
      this.formInline.workId = data[0].id
      this.workOrderVisible = false
    },
    // 修改批量删除方法
    handleBatchDelete() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      const selectedIds = new Set(this.multipleSelection.map((item) => item.id))
      this.tableData = this.tableData.filter((item) => !selectedIds.has(item.id))
    },
    // 新增删除操作方法
    onOperate(row) {
      const index = this.tableData.findIndex((item) => item === row)
      if (index !== -1) {
        this.tableData.splice(index, 1)
      }
    },
    handleQuantityChange(row) {
      // 自动计算金额（需要确保row中有materialUnitPrice字段）
      if (row.unitPrice && row.operateCount) {
        row.sumAmount = (row.unitPrice * row.operateCount).toFixed(2)
      } else {
        row.sumAmount = ''
      }
    },
    // 验证数量字段
    validateQuantity(row) {
      if (!row.operateCount) {
        this.$message.warning('出库数量不能为空')
        return false
      }
      if (row.operateCount <= 0) {
        this.$message.warning('出库数量必须大于0')
        row.operateCount = ''
        return false
      }
      // 确保数量为正整数
      row.operateCount = Math.abs(parseInt(row.operateCount))
      // 重新计算金额
      this.handleQuantityChange(row)
      return true
    },
    // 模板下载
    exportImportTemplete() {
      // 从store中获取用户信息，如果store中没有则从LOGINDATA中获取
      let hospitalCode, unitCode
      if (this.$store.getters['user/isLogin']) {
        const userInfo = this.$store.state.user.userInfo.user
        hospitalCode = userInfo.hospitalCode ?? 'BJSJTYY'
        unitCode = userInfo.unitCode ?? 'BJSYGJ'
      } else {
        const baseInfo = JSON.parse(sessionStorage.getItem('LOGINDATA') || '{}')
        hospitalCode = baseInfo.hospitalCode || 'BJSJTYY'
        unitCode = baseInfo.unitCode || 'BJSYGJ'
      }
      const data = {
        unitCode: unitCode,
        hospitalCode: hospitalCode
      }
      axios({
        method: 'post',
        url: __PATH.BASE_URL_HSC + 'outwarehouseRecord/exportImportTemplete',
        params: data,
        responseType: 'arraybuffer',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + this.$store.state.user.token
        }
      }).then((res) => {
        if (res.status == 200 && !res.data.code) {
          let name = '耗材模板.xls'
          let blob = new Blob([res.data])
          let url = URL.createObjectURL(blob)
          let a = document.createElement('a')
          a.href = url
          a.download = decodeURI(name)
          a.click()
          window.URL.revokeObjectURL(url)
          if (res.data.code == 500) {
            this.$message.error(res.data.message)
          } else if (res.status == 200 && !res.data.code) {
            this.$message.success('下载成功')
          } else {
            this.$message.error('下载失败')
          }
        }
      })
    },
    // 点击新增
    showaddConsumables() {
      if (!this.formInline.warehouseId) {
        this.$message.warning('请先选择出库仓库')
        return
      }
      this.exportCatch = 0
      this.dialogVisible = true
    },
    closeDialog() {
      this.dialogVisible = false
    },
    closeWorkOrderDialog() {
      this.workOrderVisible = false
    },
    // 审核提交
    submitAudit() {
      this.$refs.auditForm.validate((valid) => {
        if (valid) {
          // 检查表格数据是否存在
          if (this.tableData.length === 0) {
            this.$message.error('请添加出库物品')
            return
          }
          // 检查每行的出库数量是否已填写
          const emptyCountRows = this.tableData.filter((row) => !row.operateCount)
          if (emptyCountRows.length > 0) {
            this.$message.error('请填写所有行的出库数量')
            return
          }
          // 获取选中的仓库对象
          const selectedWarehouse = this.warehouseList.find((item) => item.id === this.formInline.warehouseId)
          // 获取申请人名称
          const selectedPerson = this.personList.find((item) => item.id === this.formInline.applicantId)
          // 获取选中的出库类型对象
          const selectedOutboundType = this.outboundTypeList.find((item) => item.id === this.formInline.outwarehouseType)
          // 获取部门名称（处理级联选择器的数组值）
          const findDeptName = (idList, list) => {
            const targetId = idList?.slice(-1)[0] // 取最后一级部门ID
            let result = ''
            const search = (arr) => {
              arr.forEach((item) => {
                if (item.id === targetId) result = item.deptName
                if (item.children) search(item.children)
              })
            }
            search(list)
            return result
          }
          this.formLoading = true
          // 第一步：先调用编辑接口保存修改的信息
          let editParam = {
            // 基本信息
            id: this.$route.query.id,
            recordNumber: this.formInline.recordNumber || '',
            outwarehouseType: this.formInline.outwarehouseType || '',
            warehouseId: this.formInline.warehouseId || '',
            outwarehouseDate: this.formInline.outwarehouseDate || '',
            outwarehouseAmount: this.formInline.outwarehouseAmount || '',
            applicantDepartmentId: this.formInline.applicantDepartmentId ? this.formInline.applicantDepartmentId[this.formInline.applicantDepartmentId.length - 1] : '',
            applicantId: this.formInline.applicantId || '',
            workNum: this.formInline.workNum || '',
            remarks: this.formInline.remarks || '',
            // 处理后的数据
            materialRecordArrayStr: JSON.stringify(this.tableData),
            userId: this.userInfo.staffId,
            userName: this.userInfo.staffName,
            status: '2', // 保持审核状态
            createSource: '0',
            warehouseName: selectedWarehouse?.warehouseName || '',
            outwarehouseTypeName: selectedOutboundType?.dictionaryDetailsName || '',
            applicantName: selectedPerson?.staffName || '',
            applicantDepartmentName: findDeptName(this.formInline.applicantDepartmentId, this.deptList)
          }
          this.$api.warehouseApi
            .outwarehouseRecordSave(editParam)
            .then((res) => {
              if (res.code == '200') {
                // 第二步：调用审核接口
                let approveParam = {
                  type: this.form.type,
                  opinion: this.form.opinion,
                  id: this.$route.query.id,
                  recordNumber: this.formInline.recordNumber,
                  userName: this.userInfo.staffName,
                  userId: this.userInfo.staffId
                }
                this.$api.warehouseApi
                  .approveOutWarehouseRecord(approveParam)
                  .then((res2) => {
                    this.formLoading = false
                    if (res2.code == '200') {
                      this.$message({
                        message: '审核提交成功',
                        type: 'success'
                      })
                      this.$router.go(-1)
                    } else {
                      this.$message({
                        message: res2.message || '审核失败',
                        type: 'error'
                      })
                    }
                  })
                  .catch((msg) => {
                    this.formLoading = false
                    this.$message.error(msg || '审核失败')
                  })
              } else {
                this.formLoading = false
                this.$message({
                  message: res.message || '保存信息失败',
                  type: 'error'
                })
              }
            })
            .catch((msg) => {
              this.formLoading = false
              this.$message.error(msg || '保存信息失败')
            })
        }
      })
    },

    // 设置申请人部门和申请人的默认值
    setDefaultValues() {
      // 只在新增模式下设置默认值，编辑模式不设置
      if (this.routeQueryType === 'edit' || this.routeQueryType === 'review') {
        return
      }

      console.log('设置默认值 - 用户信息:', this.userInfo)

      if (this.userInfo && this.userInfo.staffId) {
        // 设置申请人部门默认值
        const userDeptId = this.userInfo.deptId || this.userInfo.officeId
        console.log('用户部门ID:', userDeptId)

        if (userDeptId && this.allDept && this.allDept.length > 0) {
          // 查找部门的完整路径以支持多级级联选择器
          const deptPath = this.findDeptPath(userDeptId)
          console.log('部门路径:', deptPath)

          if (deptPath.length > 0) {
            this.formInline.applicantDepartmentId = deptPath
            // 加载该部门的人员列表，然后设置申请人默认值
            this.getPersonList([userDeptId]).then(() => {
              // 在人员列表加载完成后设置申请人默认值
              this.$nextTick(() => {
                this.$set(this.formInline, 'applicantId', this.userInfo.staffId)
                console.log('设置申请人ID:', this.userInfo.staffId)
              })
            })
          } else {
            // 如果找不到完整路径，使用简单格式
            console.log('使用简单格式设置部门')
            this.formInline.applicantDepartmentId = [userDeptId]
            this.getPersonList([userDeptId]).then(() => {
              this.$nextTick(() => {
                this.$set(this.formInline, 'applicantId', this.userInfo.staffId)
                console.log('设置申请人ID:', this.userInfo.staffId)
              })
            })
          }
        } else {
          // 如果没有部门信息，直接设置申请人
          console.log('没有部门信息，直接设置申请人')
          this.$set(this.formInline, 'applicantId', this.userInfo.staffId)
        }
      } else {
        console.log('用户信息不完整:', this.userInfo)
      }
    },

    // 查找部门的完整路径（支持多级级联选择器）
    findDeptPath(deptId) {
      if (!this.allDept || !Array.isArray(this.allDept) || this.allDept.length === 0) {
        console.warn('部门数据尚未加载或为空')
        return []
      }

      console.log('查找部门路径，目标部门ID:', deptId, '类型:', typeof deptId)
      console.log(
        '所有部门数据:',
        this.allDept.map((d) => ({ id: d.id, name: d.deptName, pid: d.pid }))
      )

      const path = []
      const findParent = (id, parentIds = []) => {
        // 使用严格相等比较，确保类型一致
        const dept = this.allDept.find((item) => String(item.id) === String(id))
        if (!dept) {
          console.warn(`未找到部门 ${id}，类型: ${typeof id}`)
          // 尝试查找所有可能的匹配
          const possibleMatches = this.allDept.filter((item) => item.id == id)
          console.log('可能的匹配:', possibleMatches)
          return parentIds
        }

        console.log(`找到部门: ${dept.deptName} (ID: ${dept.id}, PID: ${dept.pid})`)
        parentIds.unshift(String(id)) // 确保路径中的ID都是字符串类型

        // 检查是否有父部门ID且不等于自身ID
        if (dept.pid && String(dept.pid) !== String(id) && String(dept.pid) !== '0') {
          return findParent(dept.pid, parentIds)
        }

        return parentIds
      }

      const result = findParent(deptId, path)
      console.log('最终部门路径:', result)
      return result
    }
  }
}
</script>
<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
}
.content_box {
  height: calc(100% - 30px);
  padding: 0 24px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}
.content-title {
  border-bottom: 1px solid #e4e7ed;
  padding: 12px 24px;
  cursor: pointer;
}
.form-inline {
  .el-input,
  .el-select,
  .el-textarea .el-cascader {
    width: 300px;
  }
}
.form-inline .cascaderWid {
  width: 740px;
}
// 空内容样式
.empty-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}
.green_line {
  display: inline-block;
  width: 5px;
  height: 16px;
  background: #3562db;
  margin-right: 6px;
  border-radius: 2px !important;
  vertical-align: middle;
}
.toptip {
  box-sizing: border-box;
  padding-left: 26px;
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-align: left;
  padding-left: 16px;
  border-bottom: 1px solid rgb(216 222 231 / 100%);
}
.mt-20 {
  margin-top: 20px;
}
.audit-form {
  padding: 0 20px;
  min-height: 200px;
}
::v-deep .audit-form .el-form-item__content {
  margin-left: 20px !important;
}
::v-deep .audit-form .el-form-item {
  margin-bottom: 10px !important;
}
::v-deep .opinion-item .el-form-item__content {
  width: 70vw;
}
</style>
