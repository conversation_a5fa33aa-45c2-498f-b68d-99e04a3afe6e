<template>
  <div class="inner">
    <el-form label-width="120px" :model="classHourInfo" ref="classHourInfo" class="demo-form-inline">
      <div class="classHourItem" v-for="(item, index) in classHourInfo.classList" :key="index">
        <!-- 课程目录 -->
        <div class="top">
          <span>课程目录</span>
          <span @click="delClassList(index)"><i class="el-icon-delete"></i>删除课时</span>
        </div>
        <div class="classHourInfo">
          <div>
            <el-form-item label="课时名称" :prop="`classList.${index}.periodName`" :rules="classHourRules.periodName">
              <el-input v-model="item.periodName" placeholder="请输入课时名称" show-word-limit maxlength="30"
                style="width: 300px"></el-input>
            </el-form-item>
            <el-form-item label="课时类型" :prop="`classList.${index}.type`" :rules="classHourRules.type">
              <el-select v-model="item.type" placeholder="请选择课时类型" style="width: 300px" @change="classTypeBtn(item)">
                <el-option v-for="item in classHourTypeList" :key="item.id" :label="item.label" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="item.type != '1'" label="课时阅读时长" :prop="`classList.${index}.duration`"
              :rules="classHourRules.duration">
              <el-input type="number" v-model="item.duration" placeholder="请输入阅读时长" min="1" style="width: 300px">
                <template slot="append">分钟</template>
              </el-input>
            </el-form-item>
          </div>
          <el-form-item v-if="item.type == '1'" label="课时视频" :prop="`classList.${index}.fileEcho`"
            :rules="classHourRules.fileEcho" class="fileClass">
            <el-upload ref="uploadFile" drag multiple class="video_file video" action="string"
              :file-list="item.fileEcho" v-loading="item.uploadLoading" :http-request="(file) => httpRequest(file, item, '0', index)" accept=".MP4,.AVI,.WMV,.MPEG" :limit="1" :on-exceed="handleExceed" :before-upload="(file) => {
                  return beforeAvatarUpload(item, file, '0');
                }" 
                :on-remove="(file,fileList)=>handleRemove(file,fileList,item,index)"
                :on-change="(file, fileList) => {
                  return fileChange(file, fileList, item);
                }">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text" style="top: 30px">添加视频</div>
              <div slot="tip" class="el-upload__tip">
                仅能添加一个视频，支持MP4、AVL、WMV、MPEG格式；文件最大不超过100M
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item v-if="item.type == '2'" label="课时文档" :prop="`classList.${index}.fileEcho`"
            :rules="classHourRules.fileEcho" v-loading="item.uploadLoading" class="fileClass">
            <el-upload ref="uploadFile" drag multiple class="video_file video" action="string"
              :file-list="item.fileEcho" :http-request="(file) => httpRequest(file, item, '1', index)" accept=".pdf,.doc,.docx,.pptx,.png,.jpg" :limit="1" :on-exceed="handleExceed" 
                :before-upload="(file) => { return beforeAvatarUpload(item, file, '1')}" 
                :on-remove="(file,fileList)=>handleRemove(file,fileList,item,index)"
                  :on-change="(file, fileList) => {
                  return fileChange(file, fileList, item);
                }">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text" style="top: 30px">添加文档</div>
              <div slot="tip" class="el-upload__tip">
                仅能添加一个文档，支持pdf、doc、pptx、png、jpg
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="课时文本" v-if="item.type == '3'" :prop="`classList.${index}.document`"
            :rules="classHourRules.document">
            <Editor ref="myTextEditor" v-model="item.document" class="my-editor"></Editor>
          </el-form-item>
        </div>
        <!-- 课后习题 -->
        <div class="exercises">
          <span>课后习题</span>
          <span>建议添加10道题以内</span>
          <span @click="addQuestions(index)"><i class="el-icon-plus"></i>添加试题</span>
          <div class="exercisesList" v-if="item.coursePeriodQuestionList.length">
            <div v-for="(k, ind) in item.coursePeriodQuestionList" :key="k.id" :name="k.id"
              :class="['exercisesItem', k.isExpand ? 'expand' : '']">
              <div class="exercisesTop">
                <div class="left">
                  <div class="exercisesType">
                    {{ getQuestionType(k.type) }}
                  </div>
                  <span>({{ k.free1 }})</span>
                </div>
                <div class="right">
                  <el-input type="number" placeholder="小题分数" min="1" style="width: 160px" v-model="k.score">
                    <template slot="append">分</template>
                  </el-input>
                  <i class="el-icon-upload2" @click="moveUp(ind, item)"></i>
                  <i class="el-icon-download" @click="moveDown(ind, item)"></i>
                  <i class="el-icon-delete" @click="deletQuestions(ind, item)"></i>
                  <div class="line"></div>
                  <span @click="k.isExpand = !k.isExpand">{{ k.isExpand ? "折叠" : "展开" }}</span>
                </div>
              </div>
              <div :class="['exercisesName', k.isExpand ? '' : 'title']">
                {{ k.topic }}
              </div>
              <el-radio-group v-if="k.type == '1'" class="radio" v-model="k.answer" disabled>
                <el-radio v-for="(j, index) in k.options" :key="index" :label="j.id">{{ j.id }}. {{ j.label
                  }}</el-radio>
              </el-radio-group>
              <el-checkbox-group v-if="k.type == '2'" v-model="k.answer" class="radio" disabled>
                <el-checkbox v-for="(j, index) in k.options" :key="index" :label="j.id">{{ j.id }}. {{ j.label
                  }}</el-checkbox>
              </el-checkbox-group>
              <div v-if="k.type=='4'">
                <div class="answerClass" v-for="(obj,index) in k.options" :key="index">
                  {{'填空'+$tools.addLetterNum(index)+'答案：'}}
                  <el-tag
                    v-for="(tag) in obj.label"
                    :key="tag.name"
                    class="camera-tag"
                    >
                    {{tag.name}}
                  </el-tag>
                </div>
              </div>
              <p v-else>答案：{{ k | getAnswer }}</p>
              <p>
                解析：
                {{ k.analysis }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </el-form>
    <el-button type="primary" @click="addClassHour">添加课时</el-button>
  </div>
</template>

<script>
import Editor from "@/components/questionEditor/Editor.vue";
import axios from "axios";

export default {
  components: {
    Editor
  },
  props: {
    coursePeriodDTOList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      classHourInfo: {
        classList: [],
      },
      classHourTypeList: [
        { id: 1, label: "视频" },
        { id: 2, label: "文档" },
        { id: 3, label: "文本" },
      ],
      classHourRules: {
        periodName: [
          { required: true, message: "请输入课时名称", trigger: "blur" },
          { min: 1, max: 30, message: "长度在 1 到 30 个字符", trigger: "blur" },
        ],
        type: [
          { required: true, message: "请选择课时类型", trigger: "change" },
        ],
        fileEcho: [
          { required: true, message: "请选择视频或文档", trigger: "change" },
        ],
        document: [
          { required: true, message: "请输入课时文本", trigger: "blur" },
        ],
        duration: [
          { required: true, message: "请输入阅读时长", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    this.classHourInfo.classList = this.coursePeriodDTOList;
  },
  filters: {
    getAnswer(val) {
      if (val.type == '3') {
        return val.answer == '1' ? '正确' : '错误';
      } else if (val.type == '2') {
        return val.answer.toString();
      } else {
        return val.answer;
      }
    }
  },
  methods: {
    // 获取题型文本
    getQuestionType(type) {
      const types = {
        '1': "单选题",
        '2': "多选题",
        '3': "判断题",
        '4': "填空题",
        '5': "简答题"
      };
      return types[type] || '未知题型';
    },
    
    // 课时类型
    classTypeBtn(item) {
      item.fileEcho = [];
      item.url = "";
      item.doucument = "";
    },
    
    // 校验是否填全
    getValidate(type) {
      this.$refs.classHourInfo.validate((valid) => {
        if (valid) {
          this.$emit("submit", type);
        }
      });
    },
    
    // 添加试题
    addQuestions(index) {
      this.$emit("addQuestion", index);
    },
    
    addClassHour() {
      this.classHourInfo.classList.push({
        periodName: "",
        type: 1,
        url: "",
        fileEcho: [],
        coursePeriodQuestionList: [],
      });
    },
    
    //删除课时
    delClassList(index) {
      if (this.classHourInfo.classList.length == 1) {
        this.$message.error('至少保留一个课时');
        return;
      }
      this.classHourInfo.classList.splice(index, 1);
    },
    
    // 上移选择项
    moveUp(index, item) {
      if (index === 0) {
        this.$message.error('禁止上移');
        return;
      }
      
      const list = item.coursePeriodQuestionList;
      [list[index], list[index - 1]] = [list[index - 1], list[index]];
      item.coursePeriodQuestionList = [...list];
    },
    
    //下移选择项
    moveDown(index, item) {
      const list = item.coursePeriodQuestionList;
      if (index === list.length - 1) {
        this.$message.error('禁止下移');
        return;
      }
      
      [list[index], list[index + 1]] = [list[index + 1], list[index]];
      item.coursePeriodQuestionList = [...list];
    },
    
    // 删除试题
    deletQuestions(index, item) {
      this.$confirm('确定删除这道试题吗?', '提示', {
        type: 'warning'
      }).then(() => {
        item.coursePeriodQuestionList.splice(index, 1);
      }).catch(() => {});
    },
    
    fileChange(file, fileList, item) {
      item.fileEcho = fileList;
    },
    
    httpRequest(file, item, type, index) {
      const formData = new FormData();
      formData.append("file", file.file);
      const url = type == '0' ? 'minio/uploadVideo' : 'minio/upload';
      item.uploadLoading = true;
      console.log('1111');
      this.$emit('isDisabled',true)
      axios({
        method: "post",
        url: __PATH.BASE_URL_LABORATORY + url,
        data: formData,
        headers: {
          token: this.routeInfo.token,
        },
      })
      .then((res) => {
        this.$emit('isDisabled',false)
        item.uploadLoading = false;
        if (res.data.code == 200) {
          item.fileEcho[0] = {
            ...item.fileEcho[0],
            url: res.data.data.viewAddress,
            ...(type == '0' ? { duration: res.data.data.videoTime } : {})
          };
          item.url = JSON.stringify(item.fileEcho);
          console.log(item.fileEcho,'item.fileEcho');
          
          this.$refs.classHourInfo.validateField(`classList.${index}.fileEcho`);
        } else {
          this.$message.error("上传失败");
          item.fileEcho = [];
        }
        this.$forceUpdate()
      })
      .catch(() => {
        item.uploadLoading = false;
        this.$message.error("上传失败");
        item.fileEcho = [];
        this.$forceUpdate()
      });
    },
    
    handleExceed() {
      this.$message.error("最多上传一个");
    },
    
    beforeAvatarUpload(item, file, type) {
      if (file.name.includes(',')) {
        this.$message.error("文件名称不能含有,号");
        return false;
      }
      
      const size = file.size / 1024 / 1024;
      if (type == '0' && size >= 100) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      
      if (type == '1' && size >= 20) {
        this.$message.error("上传文件大小不能超过 20MB!");
        return false;
      }
      
      return true;
    },
    
    handleRemove(file, fileList, item, index) {
      item.url = "";
      item.fileEcho = fileList;
      this.$refs.classHourInfo.validateField(`classList.${index}.fileEcho`);
    }
  },
};
</script>

<style lang="scss" scoped>
.inner {
  background-color: #f5f5fa;
}

.classHourInfo {
  display: flex;
}

.classHourItem {
  padding: 16px;
  background-color: #fff;
  position: relative;
  margin-bottom: 10px;

  .top {
    display: flex;
    justify-content: space-between;

    span:nth-child(2) {
      cursor: pointer;
      color: red;

      i {
        margin-right: 5px;
      }
    }
  }
}

.exercises {
  span:nth-child(2) {
    color: #ccced3;
    font-size: 14px;
    margin: 0 10px;
  }

  span:nth-child(3) {
    color: #3562db;
    font-size: 14px;
    cursor: pointer;
  }

  .exercisesList {
    margin-top: 10px;
    padding: 16px;
    font-size: 14px;
    background-color: #f5f5fa;

    .exercisesItem {
      height: 100px;
      overflow: hidden;
      background-color: #fff;
      margin-bottom: 16px;
      padding: 16px;
      border-bottom: 4px solid #faf9fc;

      .exercisesTop {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;

        .left {
          display: flex;
          align-items: center;
          justify-content: center;

          span {
            color: #7f848c;
          }
        }

        .right {
          color: #ccced3;
          display: flex;
          align-items: center;

          .line {
            width: 2px;
            height: 14px;
            margin: 0 10px 0 26px;
            background-color: #dcdfe6;
          }

          span {
            color: #3562db;
            margin-left: 16px;
            cursor: pointer;
          }

          i {
            color: #3562db;
            cursor: pointer;
            margin-left: 16px;
          }
        }

        .exercisesType {
          width: 58px;
          height: 22px;
          line-height: 22px;
          text-align: center;
          border-radius: 4px;
          color: #86909c;
          background-color: #ededf5;
          margin: 0 10px;
        }
      }

      .exercisesName {
        line-height: 20px;
        margin-bottom: 16px;
        word-break: break-all;
      }

      .title {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
      }

      .el-radio {
        margin-left: 38px;
        font-size: 14px;
        color: #7f848c !important;
        line-height: 18px;
        display: flex;
      }

      p {
        font-size: 14px;
        color: #7f848c !important;
        line-height: 20px;
        margin-bottom: 16px;
      }
    }

    .expand {
      height: auto;
    }
  }
}

.video_file {
  width: 412px;
  height: 100px;
  position: absolute;
  line-height: 90px;
}

::v-deep .el-collapse-item__arrow {
  display: none;
}

::v-deep .el-collapse-item__header {
  height: 100px;
  display: block;
  flex: none;
  border-bottom: none;
}

.video_file>.el-upload__tip {
  width: 260px;
  line-height: 20px;
  position: relative;
  right: -170px;
  top: -84px;
}

::v-deep .video_file>.el-upload-list {
  position: absolute;
  top: 0;
  width: 260px;
  margin-left: 430px;
  max-height: 100px;
}

::v-deep .video_file .el-upload .el-upload-dragger {
  width: 160px;
  height: 100px;
}

::v-deep .video_file .el-upload__text {
  position: absolute;
  left: 50px;
  color: #606266;
  font-size: 14px;
  text-align: center;
}

::v-deep .el-upload-dragger .el-icon-upload {
  font-size: 30px;
  color: #c0c4cc;
  margin: 40px 0 16px;
  line-height: 5px;
}

::v-deep .ql-container {
  height: calc(100% - 60px);
}

::v-deep .fileClass .el-form-item__error {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 260%;
  width: 300px;
  left: 6px;
}

::v-deep .el-radio {
  display: block;
  margin: 10px 0;

  .el-radio__label {
    white-space: normal;
  }
}

::v-deep .el-checkbox {
  display: block;
  margin: 10px 0;
}

::v-deep .el-checkbox-group {
  margin-left: 38px;
}

.answerClass {
  margin-bottom: 10px;
  color: #a4a7ad;
}

.camera-tag {
  background: #f6f5fa;
  border-radius: 4px;
  font-size: 14px;
  font-family: 'PingFang SC-Regular', 'PingFang SC';
  font-weight: 400;
  color: #121f3e !important;
  border: none;
  margin:0 8px;
  ::v-deep .el-tag__close {
    color: #121f3e;
    &:hover {
      color: #fff;
      background-color: #3562db;
    }
  }
}
</style>