<template>
  <PageContainer v-if="routeInfo && routeInfo.isFalg == 0">
    <div slot="content">
      <div class="content " :class="activeName=='first'?'top-box':'top-boxCopy'">
        <div class="tabsMenu">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="学习情况统计" name="first"></el-tab-pane>
            <el-tab-pane label="考试情况统计" name="second"></el-tab-pane>
          </el-tabs>
        </div>
        <div class="sectionAll">
          <el-select clearable @change="changeSelect" filterable v-model="deptId" placeholder="全部部门"
            style="width: 200px">
            <el-option v-for="item in deptAllList" :key="item.id" :label="item.teamName" :value="item.id">
            </el-option>
          </el-select>
        </div>
        <div class="title" v-if="activeName=='second'">
          <svg-icon name="right-arrow" />
          <span>{{activeName=='second'?'考试':'学习'}}情况统计</span>
        </div>
        <div class="examInfo" v-if="activeName=='second'">
          <div class="top-left">
            <div class="item itemCopy">
              <div class="val-box">
                <span class="desc">考试通过率 <span class="detailsSpan" @click.stop="clickDetailsExam">详情</span></span>
                <div class="text-box">
                  <span class="numClassSpan">{{examData.totalPassingRate}}</span>
                </div>
              </div>

            </div>
            <div class="item itemCopy">
              <div class="val-box">
                <span class="desc">考试任务完成率 <span class="detailsSpan" @click.stop="clickDetailsExam">详情</span></span>
                <div class="text-box">
                  <span class="numClassSpan">{{examData.totalFinishRate}}</span>
                </div>
              </div>

            </div>
          </div>

        </div>
        <div class="surround" v-if="activeName=='first'">
          <div class="top-left">
            <div class="item">
              <div class="val-box">
                <span class="desc">总学习人数</span>
                <div class="text-box">
                  <span class="numClassSpan">{{learningSituation.personCount||0}}</span>
                </div>
              </div>
            </div>
            <div class="item">
              <div class="val-box">
                <span class="desc">当前在线人数</span>
                <div class="text-box">
                  <span class="numClassSpan">{{learningSituation.onlineCount||0}}</span>
                </div>
              </div>

            </div>
            <div class="item">
              <div class="val-box">
                <div class="desc">
                   <span>学习任务完成率</span>
                   <span class="detailsSpan" @click.stop="clickDetails">详情</span>
                </div>
                <div class="text-box">
                  <span class="numClassSpan">{{learningSituation.rate || '0%'}}</span>
                </div>
              </div>

            </div>
            <div class="item">
              <div class="val-box">
                <span class="desc">人均学习时长(周)</span>
                <div class="text-box">
                  <span class="numClassSpan">{{learningSituation.weekData||0}}</span>
                  <span style="margin-top: 10px;font-size: 14px;color: #96989A;">分钟</span>
                  <span class="average">
                    <span class="average-title">环比</span>
                    <span v-if="learningSituation.weekDataRate != '0.00'">
                      <span
                        :class="learningSituation.weekDataRate.charAt(0) == '-' ? 'greenColor' : 'redColor'">{{ learningSituation.weekDataRate }}</span>
                      <img v-if="learningSituation.weekDataRate.charAt(0) == '-'"
                        src="@/assets/images/service/down.png" />
                      <img v-else src="@/assets/images/service/up.png" />
                    </span>
                    <span v-else>{{ learningSituation.weekDataRate }}</span>
                  </span>
                </div>

              </div>

            </div>
            <div class="item">
              <div class="val-box">
                <span class="desc">人均学习时长(月)</span>
                <div class="text-box">
                  <span class="numClassSpan">{{learningSituation.monthData}}</span>
                  <span style="margin-top: 10px;font-size: 14px;color: #96989A;">分钟</span>
                  <span class="average">
                    <span class="average-title">环比</span>
                    <span v-if="learningSituation.monthDataRate != '0.00'">
                      <span
                        :class="learningSituation.monthDataRate.charAt(0) == '-' ? 'greenColor' : 'redColor'">{{ learningSituation.monthDataRate }}</span>
                      <img v-if="learningSituation.monthDataRate.charAt(0) == '-'"
                        src="@/assets/images/service/down.png" />
                      <img v-else src="@/assets/images/service/up.png" />
                    </span>
                    <span v-else>{{ learningSituation.monthDataRate }}</span>
                  </span>
                </div>
              </div>

            </div>
          </div>

        </div>
      </div>
      <div class="bottom-box bottom-box-exam" v-if="activeName=='second'">
        <div class="right">
          <div class="title">
            <svg-icon name="right-arrow" />
            <span>答题错误率Top10</span>
          </div>
          <el-carousel :interval="2000" arrow="never" autoplay loop height='520px'>
            <el-carousel-item v-for="(chunk, index) in chunkedData" :key="index">
              <el-table stripe v-loading="tableLoading" :data="chunk" border style="width: 100%;" height="86%"
                size="small" :show-header='false'>
                <el-table-column label="" prop="index" width="50" align="center" :resizable="false">
                  <template slot-scope="scope">
                    <span class="indexSpan"
                      :class="scope.row.index ==1 ? 'indexSpanFirst' :scope.row.index==2 ? 'indexSpanSecond':scope.row.index==3 ?'indexSpanThird':''">{{scope.row.index}}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="question" label="" align="center" show-overflow-tooltip :resizable="false">
                </el-table-column>
                <el-table-column prop="rate" label="" align="center" show-overflow-tooltip :resizable="false">
                </el-table-column>
              </el-table>
            </el-carousel-item>
          </el-carousel>
        </div>
        <div class="right">
          <div class="title">
            <svg-icon name="right-arrow" />
            <span>近5次考试平均分Top5</span>

          </div>
          <el-table stripe v-loading="tableLoading" :data="tableData" border style="width: 100%;" height="90%"
            size="small" :show-header='false'>
            <el-table-column label="" prop="index" width="50" align="center" :resizable="false">
              <template slot-scope="scope">
                <span class="indexSpan"
                  :class="scope.row.index==1 ? 'indexSpanFirst' :scope.row.index==2 ? 'indexSpanSecond':scope.row.index==3 ?'indexSpanThird':''">{{scope.row.index}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="deptName" label="科室" align="center" show-overflow-tooltip :resizable="false">
            </el-table-column>
            <el-table-column prop="avgScore" label="分数
            " align="center" show-overflow-tooltip :resizable="false">
            </el-table-column>

          </el-table>
        </div>
      </div>

      <div class="bottom-box" v-if="activeName=='first'">
        <div class="right">
          <div class="title">
            <svg-icon name="right-arrow" />
            <span>学习时长Top5</span>
            <div class="btns">
              <span :class="{ 'active-btn': orderType == 'member' }" @click="changeOrderType('member')">成员</span>
              <span :class="{ 'active-btn': orderType == 'department' }"
                :style="{ cursor: deptId ? 'not-allowed' : 'pointer' }"
                @click="!deptId && changeOrderType('department')">部门</span>
            </div>
          </div>
          <el-table stripe v-loading="tableLoading" :data="dataLearningTime" border style="width: 100%;" height="86%"
            size="small" :show-header='false'>
            <el-table-column label="" prop="index" width="50" align="center" :resizable="false">
              <template slot-scope="scope">
                <span class="indexSpan"
                  :class="scope.$index==0 ? 'indexSpanFirst' :scope.$index==1 ? 'indexSpanSecond':scope.$index==2 ?'indexSpanThird':''">{{scope.$index+1}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="" align="center" show-overflow-tooltip :resizable="false">
            </el-table-column>
            <el-table-column prop="count" label="" align="center" show-overflow-tooltip :resizable="false">
            </el-table-column>

          </el-table>
        </div>
        <div class="right">
          <div class="title">
            <svg-icon name="right-arrow" />
            <span>课程资源利用率Top5</span>
            <div class="btnsCopy">
              <span :class="{ 'active-btn': orderTypeCopy == 'duration' }"
                @click="changeOrderTypeCopy('duration')">时长</span>
              <span :class="{ 'active-btn': orderTypeCopy == 'frequency' }"
                @click="changeOrderTypeCopy('frequency')">频次</span>
            </div>
          </div>
          <el-table stripe v-loading="tableLoading" :data="dataCourseTime" border style="width: 100%;" height="86%"
            size="small" :show-header='false'>
            <el-table-column label="" prop="index" width="50" align="center" :resizable="false">
              <template slot-scope="scope">
                <span class="indexSpan"
                  :class="scope.$index==0 ? 'indexSpanFirst' :scope.$index==1 ? 'indexSpanSecond':scope.$index==2 ?'indexSpanThird':''">{{scope.$index+1}}</span>
              </template>
            </el-table-column>
            <el-table-column :prop="orderTypeCopy == 'duration'?'course_name':'name'"  align="center" show-overflow-tooltip :resizable="false">
            </el-table-column>
            <el-table-column prop="totalTime" label="" align="center" show-overflow-tooltip :resizable="false">
            </el-table-column>

          </el-table>
        </div>
      </div>
      <div class="content middle-box" v-if="activeName=='first'">
        <div class="title">
          <svg-icon name="right-arrow" />
          <span>活跃时段分布</span>
          <div class="btnsTime">
            <el-date-picker @change=changeTime v-model="startTime" type="date" placeholder="选择日期">
            </el-date-picker>
          </div>
        </div>
        <div id="orderTrendChart"></div>
      </div>

    </div>
  </PageContainer>
  <div v-else>
    <permissionPrompt></permissionPrompt>
  </div>
</template>
<script>
  import * as echarts from 'echarts'
  import template from '@/views/serviceQuality/reportManagement/template.vue'
  import permissionPrompt from "@/views/safety/courseIndex/components/permissionPrompt.vue";
  export default {
    components: {
      template,
      permissionPrompt
    },
    name: 'dataOverviewEcharts',
    data() {
      return {
        startTime: new Date(),
        value: "",
        value1: "",
        activeName: "first",
        percentage: 0,
        tableLoading: false,
        tableData1: [],
        tableData: [],
        totalCostDateType: 'year',
        orderType: 'member',
        orderTypeCopy: 'duration',
        totalPrice: 0,
        orderStatisticsData: [],
        orderTrendLastList: [],
        orderTrendList: [],
        deptId: "",
        deptAllList: [],
        learningSituation: {
          personCount:'0',
          onlineCount:'0',
          rate:'0%',
          weekData:'0',
          weekDataRate:'0.00',
          monthData:'0',
          monthDataRate:'0.00',
        },
        dataLearningTime: [],
        dataCourseTime: [],
        memberMentType: 0,
        durationFreType: 0,
        routeInfo: {},
        hoursList: [],
        appList: [],
        webList: [],
        totalList: [],
        examData:{}
      }
    },
    computed: {
      chunkedData() {
        const chunkSize = 5;
        const result = [];
        for (let i = 0; i < this.tableData1.length; i += chunkSize) {
          result.push(this.tableData1.slice(i, i + chunkSize));
        }
        console.log(result,'result');
        
        return result;
      }
    },
    watch: {
      '$store.state.settings.sidebarCollapse': {
        handler(val) {
          this.$nextTick(() => {
            let echartsDom = ['orderTrendChart', 'orderTypeCharts']
            setTimeout(() => {
              echartsDom.forEach((item) => {
                echarts.init(document.getElementById(item)).resize()
              })
            }, 250)
          })
        },
        deep: true
      }
    },
    created() {
      this.routeInfo = JSON.parse(sessionStorage.getItem('routeInfo'))
      if(!this.routeInfo) return
    },
    mounted() {
      this.getdeptList()
      // 学习情况统计
      this.getLearningSituationCount()
      // 学习时长前五
      this.getLearningTimeTopFive()
      // 课程资源前五
      this.getCourseTimeTopFive()
      // 活跃时段分布
      this.getActivePeriodCount()
    },
    methods: {
      formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day} 00:00:00`;
      },
      // 获取组织
      getdeptList() {
        this.$api.getDeptListLaboratory({}).then((res) => {
          this.deptAllList = res.data.list
        });
      },
      //时间筛选活跃时段
      changeTime(val) {
        this.getActivePeriodCount()
      },
      //部门搜索
      changeSelect() {
        if (this.activeName=='first') {
          this.studyReset()
        }else{
          this.examRest()
        }
      },
      //切换tabs
      handleClick(val) {
        if (val.index == 0) {
          this.studyReset()
        }else{
          this.examRest()
        }
      },
      studyReset(){
        this.orderType= 'member',
        this.orderTypeCopy= 'duration',
        this.learningSituation = {
          personCount:'0',
          onlineCount:'0',
          rate:'0%',
          weekData:'0',
          weekDataRate:'0.00',
          monthData:'0',
          monthDataRate:'0.00',
        }
        this.dataLearningTime = []
        this.dataCourseTime = []
        this.hoursList = []
        this.appList = []
        this.webList = []
        this.totalList = []
        this.getLearningSituationCount()
        this.getLearningTimeTopFive()
        this.getCourseTimeTopFive()
        this.getActivePeriodCount()
      },
      examRest(){
        this.getExamPassingRate()
        this.getAnswerErrorRateTopTen()
        this.getAvgScoreLastFiveTopFive()
      },
      //活跃时段分布
      getActivePeriodCount() {
        this.$api.activePeriodCount({
          startTime: this.formatDate(this.startTime),
          deptCode: this.deptId
        }).then((res) => {
          this.hoursList = res.data.hours
          this.appList = res.data.app
          this.webList = res.data.web
          this.totalList = res.data.total
          this.$nextTick(() => {
            this.initOrderTrendChart()
          })
        })
      },
      //课程资源利用率Top5
      getCourseTimeTopFive() {
        this.$api.courseTimeTopFive({
          deptCode: this.deptId,
          type: this.durationFreType
        }).then((res) => {
          this.dataCourseTime = res.data
        })
      },
      //学习时长Top5
      getLearningTimeTopFive() {
        this.$api.learningTimeTopFive({
          deptCode: this.deptId,
          type: this.memberMentType
        }).then((res) => {
          this.dataLearningTime = res.data

        })
      },
      //学习情况统计
      getLearningSituationCount() {
        this.$api.learningSituationCount({
          deptCode: this.deptId
        }).then((res) => {
          this.learningSituation = res.data
        })
      },
      // 考试通过率
      getExamPassingRate(){
        this.$api.examPassingRate({
          deptCode: this.deptId
        }).then((res) => {
          this.examData = res.data
        })
      },
      //答题错误率top10
      getAnswerErrorRateTopTen(){
        this.$api.answerErrorRateTopTen({
          deptCode: this.deptId
        }).then((res) => {
          res.data.forEach((item,index)=>{
            item.index = index+1
          })
          this.tableData1 = res.data
        })
      },
      //答题错误率top10
      getAvgScoreLastFiveTopFive(){
        this.$api.avgScoreLastFiveTopFive({
          deptCode: this.deptId
        }).then((res) => {
          res.data.forEach((item,index)=>{
            item.index = index+1
          })
          this.tableData = res.data
        })
      },
      //点击详情跳转
      clickDetails() {
        this.$router.push({
          name: 'learningRate',
          query: {
            deptCode: this.deptId
          }
        })
      },
      //考试情况跳转详情
      clickDetailsExam() {
        this.$router.push({
          name: 'examRate',
          query: {
            deptId: this.deptId
          }
        })
      },
      initOrderTrendChart() {
        let chartData = []
        this.orderTrendList.forEach((item) => {
          chartData.push({
            name: item.weeks,
            value: item.workNum
          })
        })
        let chartData2 = []
        this.orderTrendLastList.forEach((item) => {
          chartData2.push({
            name: item.weeks,
            value: item.workNum
          })
        })
        const getchart = echarts.init(document.getElementById('orderTrendChart'))
        const option = {
          tooltip: {
            trigger: 'axis',
            formatter: function (params) {
              let str = params[0].axisValue + '点<br/>'
              params.forEach((item) => {
                str += item.marker + item.seriesName + '：' + item.value + '人<br/>'
              })
              return str
            }
          },
          grid: {
            top: '15%',
            left: '1%',
            right: '1%',
            bottom: '6%',
            containLabel: true
          },
          legend: {
            data: ['App', 'Web', '全部']
          },
          xAxis: {
            type: 'category',
            data: this.hoursList,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              interval: 0
            }
          },
          yAxis: [{
            type: 'value',
            min: 0,       // 从0开始
            max: 6,      // 设置最大值以展示完整数据范围
            interval: 1,  // 刻度间隔为1
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          }],
          series: [{
              name: 'App',
              data: this.appList,
              type: 'line',
              symbol: 'circle',
              symbolSize: 6,
              itemStyle: {
                color: '#FF9435'
              },
              lineStyle: {
                color: '#FF9435'
              }
            },
            {
              name: 'Web',
              data: this.webList,
              type: 'line',
              symbol: 'circle',
              symbolSize: 6,
              itemStyle: {
                color: '#4764CC'
              },
              lineStyle: {
                color: '#4764CC'
              }
            },
            {
              name: '全部',
              data: this.totalList,
              type: 'line',
              symbol: 'circle',
              symbolSize: 6,
              itemStyle: {
                color: '#c3e195'
              },
              lineStyle: {
                color: '#c3e195'
              }
            }
          ]
        }
        getchart.clear()
        getchart.setOption(option)
        // 随着屏幕大小调节图表
        window.addEventListener('resize', () => {
          getchart.resize()
        })
      },
      changeOrderType(val) {
        this.orderType = val
        this.memberMentType = val == 'member' ? 0 : 1
        this.getLearningTimeTopFive()
      },
      changeOrderTypeCopy(val) {
        console.log(val,'val');
        
        this.orderTypeCopy = val
        this.durationFreType = val == 'duration' ? 0 : 1
        this.getCourseTimeTopFive()
      },
    }
  }

</script>
<style lang="scss" scoped>
  .container-content>div {
    height: 100%;
    display: flex;
    align-items: apace-between;
    flex-wrap: wrap;
  }

  .tabsMenu {
    margin-bottom: 15px;

    :deep(.el-tabs__item) {
      width: 100px;
    }
  }

  .content {
    width: 100%;
    background-color: #fff;
    border-radius: 4px;
    padding: 12px;
  }

  .top-box {
    height: 34%;
  }

  .top-boxCopy {
    height: 35%;
  }

  .middle-box {
    height: 34%;
  }

  .bottom-box {
    width: 100%;
    height: 29.8%;
    display: flex;
    justify-content: space-between;
  }

  .bottom-box>div {
    width: 32.8%;
    background-color: #fff;
    border-radius: 4px;
    padding: 12px;
  }

  .title span {
    font-size: 15px;
  }

  .surround {
    display: flex;
    height: 60%;
    margin-top: 10px;
    justify-content: space-between;
  }

  .top-left {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .top-left>div {
    background-color: #F7F8FA;
    width: 18%;
    padding: 0 12px;
    box-sizing: border-box;
    align-items: center;
    height: 100%;
    border-radius: 5px;
  }

  .top-left>.item>img {
    width: 60px;
    height: 60px;
    margin: 8px 0;
  }

  .response>div:nth-child(1) {
    width: 60%;
    height: 73%;
    display: flex;
    align-items: center;
    // justify-content: space-between;
  }

  .response>div:nth-child(2) {
    width: 30%;
  }

  .numClassSpan {
    font-size: 40px;
    font-weight: 500;
  }

  .desc {
    display: flex;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    font-size: 15px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    color: #121f3e;
  }

  .sectionAll {
    margin-bottom: 15px;
  }

  .top-left>div .time {
    font-size: 20px;
    font-weight: 700;

  }

  .itemCopy {
    width: 49% !important;
    height: 96% !important;
  }

  .val-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .text-box {
    height: calc(100% - 40px);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .text-box .str {
    font-size: 14px;
    color: #C2C4C8;
  }

  .text-box .num {
    font-weight: 700;
    font-size: 24px;
    color: #333333;
  }

  .average {
    margin-left: 20px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
  }

  .average .average-title {
    font-size: 12px;
    color: #666;
  }

  .average img {
    width: 20px;
    height: 20px;
  }

  .average>span:nth-child(2) {
    font-size: 16px;
    display: flex;
    align-items: center;
  }

  .average>span:nth-child(2)>span:nth-child(1) {
    margin-right: 8px;
  }

  .greenColor {
    color: #00BC6D;
  }

  .redColor {
    color: #FA403C;
  }

  .surround .top-middle {
    width: 33.5%;
    display: flex;
    justify-content: space-between;
  }

  .surround .top-middle>div {
    background-color: #faf9fc;
  }

  .surround .top-middle .order-num {
    width: 68%;
    padding: 8% 12px 5%;
    box-sizing: border-box;
  }

  .surround .top-middle .complaint {
    width: 30%;
  }

  .surround .top-middle .order-num .content {
    display: flex;
    // justify-content: space-between;
  }

  .surround .top-middle .order-num .content .left-cont {
    display: flex;
    flex-direction: column;
    margin-right: 35%;
  }

  .surround .top-middle .order-num .content .left-cont>span:nth-child(1),
  .surround .top-middle .order-num .content .right-cont>span:nth-child(1) {
    font-size: 14px;
    color: #3562db;
    font-weight: 500;
    margin-bottom: 5px;
  }

  .surround .top-middle .order-num .content .left-cont>span:nth-child(2)>span:nth-child(1),
  .surround .top-middle .order-num .content .right-cont>span:nth-child(2)>span:nth-child(1) {
    font-size: 24px;
    font-weight: 700;
    margin-right: 5px;
  }

  .surround .top-middle .order-num .content .left-cont>span:nth-child(2)>span:nth-child(2),
  .surround .top-middle .order-num .content .right-cont>span:nth-child(2)>span:nth-child(2) {
    font-size: 14px;
    color: #ccced3;
    font-weight: 500;
  }

  .surround .top-middle .order-num .content .right-cont {
    display: flex;
    flex-direction: column;
  }

  .el-progress {
    margin-bottom: 24px;
  }

  .complaint {
    padding: 8% 12px 5%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }

  .complaint>span {
    color: #3562db;
    font-size: 15px;
    display: inline-block;
    margin-bottom: 25%;
  }

  .complaint img {
    width: 40px;
    height: 38px;
  }

  .complaint>div {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .complaint>div>span>span:nth-child(1) {
    font-size: 28px;
    color: #121f3e;
    font-weight: 700;
    margin-right: 5px;
  }

  .complaint>div>span>span:nth-child(2) {
    font-size: 14px;
    color: #ccced3;
    font-weight: 500;
  }

  .surround .top-right {
    width: 29%;
    background: #faf9fc;
    padding: 1.8%;
  }

  .surround .top-right .content {
    background-color: #fff;
    width: 100%;
    height: 100%;
    border-radius: 4px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .surround .cost-title>span {
    font-size: 15px;
    color: #3562db;
  }

  .surround .top-right .content img {
    width: 40px;
    height: 38px;
  }

  .cost-num {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .cost-num>span>span:nth-child(1) {
    font-size: 28px;
    color: #121f3e;
    font-weight: 700;
    margin-right: 5px;
  }

  .cost-num>span>span:nth-child(2) {
    font-size: 14px;
    color: #ccced3;
    font-weight: 500;
  }

  #orderTrendChart,
  #orderTypeCharts,
  #dutyCharts {
    width: 100%;
    height: 90%;
    margin-top: 14px;
  }

  .el-table {
    margin-top: 14px;
  }

  .btns {
    position: absolute;
    width: 100px;
    height: 28px;
    right: 52%;
    top: 36%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .btns>span {
    width: 48%;
    background-color: #f6f5fa;
    font-size: 14px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #ededf5;
    color: #7f848c;
    cursor: pointer;
  }

  .btnsCopy {
    position: absolute;
    width: 100px;
    height: 28px;
    right: 2%;
    top: 36%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .btnsCopy>span {
    width: 48%;
    background-color: #f6f5fa;
    font-size: 14px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #ededf5;
    color: #7f848c;
    cursor: pointer;
  }

  .btnsTime {
    position: absolute;
    width: 150px;
    height: 28px;
    left: 10%;
    top: 66.7%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .active-btn {
    background-color: #3562db !important;
    color: #fff !important;
    border-color: #3562db !important;
  }

  .bottom-box .right {
    width: 49.8%;
  }

  .detailsSpan {
    font-size: 14px;
    color: #3c65c8;
    cursor: pointer;
  }

  .indexSpan {
    display: inline-block;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    text-align: center;
    line-height: 20px;
    font-weight: 700;
  }

  .indexSpanFirst {
    color: #cf7e07;
    background-color: #fbd75f;
  }

  .indexSpanSecond {
    color: #959da6;
    background-color: #cad3de;
  }

  .indexSpanThird {
    color: #b66851;
    background-color: #ecae7d;
  }
.examInfo{
  display: flex;
  height: 50%;
  margin-top: 10px;
  justify-content: space-between;
}
.bottom-box-exam{
  height: 63%;
}
</style>
