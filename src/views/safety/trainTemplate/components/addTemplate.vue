<template>
  <PageContainer :footer="true" v-loading="blockLoading">
    <div slot="content" class="table-content">
      <div class="content_box">
        <h1>基础信息</h1>
        <el-form ref="formInline" :model="formInline" class="form-inline" label-width="120px" :rules="rules">
          <el-form-item label="培训模板名称:" prop="name">
            <el-input v-model.trim="formInline.name" placeholder="请输入培训模板名称" maxlength="30" show-word-limit
              style="width: 600px;"></el-input>
          </el-form-item>
          <span style="position: relative; top: 28px; left: 40px; color: red;">*</span> <el-form-item label="培训课件:">
            <el-upload ref="uploadFile" drag multiple class="mterial_file" action="string" :file-list="fileEcho"
              :http-request="httpRequest"
              accept=".jpg,.png,.pdf,.JPG,.PBG,.GIF,.BMP,.PDF,.mp4,.avi,.wimv,.mpeg,.pdf,.doc,.docx,pptx" :limit="30"
              :on-exceed="handleExceed" :before-upload="beforeAvatarUpload" :on-remove="handleRemove" 
              :on-change="fileChange">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text" style="top: 100px">
                将文件拖到此处，或
                <em>点击上传</em>
              </div>
            </el-upload>
            <div>
            </div>
          </el-form-item>
          <el-form-item label="培训模板描述：" prop="describe">
            <el-input v-model.trim="formInline.disc" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }"
              placeholder="请输入培训模板描述:" maxlength="200" show-word-limit style="width: 600px;"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div slot="footer">
      <el-button type="primary" plain @click="$router.go('-1')">取消</el-button>
      <el-button type="primary" @click="complete">确认</el-button>
    </div>
  </PageContainer>
</template>
<script>
import axios from 'axios'
export default {
  name: "addLocationPoint",
  data() {
    return {
      readonly: false,
      formInline: {
        name: "",
        disc: '',
        courseware: [], // 附件
      },
      id: "",
      blockLoading: false,
      fileEcho: [],
      indextype: '',
      rules: {
        name: [
          { required: true, message: "请输入培训模板名称", trigger: "change" },
        ],
        fileList: [
          { required: true, message: "请上传培训课件", trigger: "change" },
        ],
      },
      fileQuestions: [],
      routeInfo: '',
      informUrl: [],
    };
  },
  created() {
    // this.routeInfo = JSON.parse(sessionStorage.getItem("routeInfo"));
    this.routeInfo = JSON.parse(sessionStorage.getItem('routeInfo'))
    this.id = this.$route.query.id
    this.indextype = this.$route.query.indexOf
    this.getTrainDetail()
  },
  methods: {
    getTrainDetail() {
      this.$api.tarainTemplateDetail({ id: this.id }).then(res => {
        if (res.code == 200) {
          this.formInline.name = res.data.name
          this.formInline.disc = res.data.disc
          this.formInline.courseware = res.data.courseware.split(',')
          res.data.fileUploadRecords.forEach(el => {
            this.fileEcho.push({
              name: el.originalFilename,
              url: el.viewAddress,
              fileRecordIds: el.id
            })
          })
        }
      })
    },
    // 点击确定
    complete() {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          if (this.indextype == 'add') {
            console.log(this.fileEcho.length,'this.fileEcho.length');
            // if (!this.fileEcho.length) {
            //   return this.$message.error("请上传培训课件");
            // }
            let data = {
              name: this.formInline.name,
              disc: this.formInline.disc,
              courseware: this.formInline.courseware.toString()
            }
            this.$api.tarainTemplateSave(data).then(res => {
              if (res.code == 200) {
                this.$message.success(res.msg)
                this.$router.go(-1);
              } else {
                this.$message.error(res.msg)
              }
            })
          } else if (this.indextype == 'detail') {
            let data = {
              id: this.id,
              name: this.formInline.name,
              disc: this.formInline.disc,
              courseware: this.formInline.courseware.toString()
            }
            console.log(data);
            this.$api.tarainTemplateEdit(data).then(res => {
              if (res.code == 200) {
                this.$message.success(res.msg)
                this.$router.go(-1);
              } else {
                this.$message.error(res.msg)
              }
            })
          }
        }
      })
    },
    async fileChange(file, fileList) {
      const isLt40M = file.size / 1024 / 1024 < 40
      if (!isLt40M) {
        // this.$message.error('上传图片大小不能超过 40MB!')
        fileList.splice(-1, 1); //移除选中图片
        return false
      }
      this.fileEcho = file
    },
    httpRequest() {
      this.formData = new FormData()
      this.formData.append('file',  this.fileEcho.raw)
      this.formData.append('hospitalCode', this.routeInfo.hospitalCode)
      axios({
        method: 'post',
        url: __PATH.BASE_URL_LABORATORY + "minio/upload",
        data: this.formData,
        headers: {
          "Content-Type": "application/json",
           token: this.routeInfo.token,
        },
      }).then(res => {
        this.formInline.courseware.push(res.data.data.fileRecordIds)
      }).catch(() => {
        this.$message.error(res.data.message)
      })
    },
    handleExceed() {
      this.$message.error('最多上传三张图片')
    },
    beforeAvatarUpload(file) {
      const isLt40M = file.size / 1024 / 1024 < 40
      if (!isLt40M) {
        this.$message.error('上传图片大小不能超过 40MB!')
        return false
      }
      if (file.name.indexOf(',') != -1) {
        this.$message.error("文件名称不能含有,号");
        return false
      }
    },
    handleRemove(file, fileList) {
      let fileListIds = []
      this.fileEcho = fileList;
      this.fileEcho.forEach((el) => {
        fileListIds.push(el.fileRecordIds)
        this.formInline.courseware = this.$set(this.formInline,'courseware',fileListIds)
      })
    },
  },
};
</script>
<style lang="scss" scoped>
.table-content {
  background: #fff;
  border-radius: 4px;
  height: calc(100% - 0px);
}

.content_box {
  height: 100%;
  padding: 30px 25px 20px 25px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

:deep(.el-input.is-disabled .el-input__inner) {
  background: #f5f7fa !important;
}

.detailClass :deep(.el-input__inner) {
  border: none !important;
}
::v-deep .el-upload-list__item {
  width: 30%;
}
.detailClass :deep(.el-textarea__inner) {
  border: none;
  resize: none;
}

.project-textarea textarea {
  height: 120px;
}

.form-inline {
  margin-top: 20px;
}
</style>
  