import Layout from '@/layout'
import EmptyLayout from '@/layout/empty'
export default [
  { 
    path: '/subjectManagement/classificationIndex',
    component: Layout,
    name: 'subjectManagement',
    meta: {
      title: '学科科目管理',
      menuAuth: '/subjectManagement/classificationIndex'
    },
    children: [
      {
        path: '',
        name: 'classificationIndex',
        component: () => import('@/views/safety/classification/classificationIndex.vue'),
        meta: {
          title: '课程分类管理',
          sidebar: false,
          breadcrumb: false,
          activeMenu: '/subjectManagement/classificationIndex'
        }
      },
    ]
  },
  {
    path: '/studyManage',
    component: Layout,
    // name: 'studyManage',
    meta: {
      title: '学习管理',
      menuAuth: '/studyManage'
    },
    children: [
      {
      path: 'curriculumReform',
      component: EmptyLayout,
      name: 'curriculumReform',
      
      meta: {
        title: '学习管理',
        menuAuth: '/studyManage/curriculumReform'
      },
      children: [
        {
          path: '',
          name: 'curriculumReform',
          component: () => import('@/views/safety/courseIndex/curriculumReform.vue'),
          meta: {
            title: '课程管理',
            sidebar: false,
            breadcrumb: false,
            activeMenu: '/studyManage/curriculumReform'
          }
        },
        {
          path: 'addCourse',
          name: 'addCourse',
          component: () => import('@/views/safety/courseIndex/components/addCourse.vue'),
          meta: {
            title: '创建课程',
            sidebar: false,
            activeMenu: '/studyManage/curriculumReform'
          }
        },
        {
          path: 'courseInfo',
          name: 'courseInfo',
          component: () => import('@/views/safety/courseIndex/components/courseInfo.vue'),
          meta: {
            title: '课程详情',
            sidebar: false,
            activeMenu: '/studyManage/curriculumReform'
          }
        },
        {
          path: 'seeFile',
          name: 'seeFile',
          component: () => import('@/views/safety/courseIndex/components/seeFile.vue'),
          meta: {
            title: '文件',
            sidebar: false,
            activeMenu: '/studyManage/curriculumReform'
          }
        },
        {
          path: 'videoPlay',
          name: 'videoPlay',
          component: () => import('@/views/safety/courseIndex/components/videoPlay.vue'),
          meta: {
            title: '视频',
            sidebar: false,
            activeMenu: '/studyManage/curriculumReform'
          }
        },
      ]
      },
      { 
      path: 'onLineCourse',
      component: EmptyLayout,
      name: 'onLineCourse',
      meta: {
        title: '在线学习',
        menuAuth: '/studyManage/onLineCourse'
      },
      children: [
        {
          path: '',
          name: 'onLineCourse',
          component: () => import('@/views/safety/courseOnline/onLineCourse.vue'),
          meta: {
            title: '在线学习',
            sidebar: false,
            breadcrumb: false,
            activeMenu: '/studyManage/onLineCourse'
          }
        },
        {
          path: 'courseDetils',
          name: 'courseDetils',
          component: () => import('@/views/safety/courseOnline/components/courseDetils.vue'),
          meta: {
            title: '课程详情',
            sidebar: false,
            activeMenu: '/studyManage/onLineCourse'
          }
        },
        {
          path: 'seeFile',
          name: 'seeFile',
          component: () => import('@/views/safety/courseIndex/components/seeFile.vue'),
          meta: {
            title: '文件',
            sidebar: false,
            activeMenu: '/studyManage/onLineCourse'
          }
        },
        {
          path: 'videoPlay',
          name: 'videoPlay',
          component: () => import('@/views/safety/courseIndex/components/videoPlay.vue'),
          meta: {
            title: '视频',
            sidebar: false,
            activeMenu: '/studyManage/onLineCourse'
          }
        },
        {
          path: 'answerIng',
          name: 'answerIng',
          component: () => import('@/views/safety/courseOnline/components/answerIng.vue'),
          meta: {
            title: '课后习题',
            sidebar: false,
            activeMenu: '/studyManage/onLineCourse'
          }
        },
      ],
      },
      { 
      path: 'learningTasks',
      component: EmptyLayout,
      name: 'learningTasks',
      meta: {
        title: '学习任务',
        menuAuth: '/studyManage/learningTasks'
      },
       children: [
        {
          path: '',
          name: 'learningTasks',
          component: () => import('@/views/safety/courseOnline/learningTasks.vue'),
          meta: {
            title: '学习任务',
            sidebar: false,
            breadcrumb: false,
            activeMenu: '/studyManage/learningTasks'
          }
        },
        {
          path: 'courseDetils',
          name: 'courseDetils',
          component: () => import('@/views/safety/courseOnline/components/courseDetils.vue'),
          meta: {
            title: '课程详情',
            sidebar: false,
            activeMenu: '/studyManage/learningTasks'
          }
        },
        {
          path: 'taskDetails',
          name: 'taskDetails',
          component: () => import('@/views/safety/courseOnline/components/taskDetails.vue'),
          meta: {
            title: '我的任务详情',
            sidebar: false,
            activeMenu: '/studyManage/learningTasks'
          }
        },
        {
          path: 'seeFile',
          name: 'seeFile',
          component: () => import('@/views/safety/courseIndex/components/seeFile.vue'),
          meta: {
            title: '文件',
            sidebar: false,
            activeMenu: '/studyManage/learningTasks'
          }
        },
        {
          path: 'videoPlay',
          name: 'videoPlay',
          component: () => import('@/views/safety/courseIndex/components/videoPlay.vue'),
          meta: {
            title: '视频',
            sidebar: false,
            activeMenu: '/studyManage/learningTasks'
          }
        },
        {
          path: 'answerIng',
          name: 'answerIng',
          component: () => import('@/views/safety/courseOnline/components/answerIng.vue'),
          meta: {
            title: '课后习题',
            sidebar: false,
            activeMenu: '/studyManage/onLineCourse'
          }
        },
      ]
      },
    ]
  },
  {
    path: '/taskAssignment/distributeTask',
    component: Layout,
    // name: 'studyManage',
    meta: {
      title: '任务派发',
      menuAuth: '/taskAssignment/distributeTask'
    },
    children: [
      {
          path: '',
          name: 'distributeTask',
          component: () => import('@/views/safety/distribute/distributeTask.vue'),
          meta: {
            title: '任务派发',
            sidebar: false,
            breadcrumb: false,
            activeMenu: '/taskAssignment/distributeTask'
          }
      },
      {
          path: 'addTask',
          name: 'addTask',
          component: () => import('@/views/safety/distribute/components/addTask.vue'),
          meta: {
            title: '创建学习任务',
            sidebar: false,
            activeMenu: '/taskAssignment/distributeTask'
          }
      },
      {
          path: 'learnTaskDetails',
          name: 'learnTaskDetails',
          component: () => import('@/views/safety/distribute/components/learnTaskDetails.vue'),
          meta: {
            title: '任务派发',
            sidebar: false,
            activeMenu: '/taskAssignment/distributeTask'
          }
      },
      {
          path: 'courseInfo',
          name: 'courseInfo',
          component: () => import('@/views/safety/courseIndex/components/courseInfo.vue'),
          meta: {
            title: '课程详情',
            sidebar: false,
            activeMenu: '/taskAssignment/distributeTask'
          }
      },
      {
          path: 'seeFile',
          name: 'seeFile',
          component: () => import('@/views/safety/courseIndex/components/seeFile.vue'),
          meta: {
            title: '文件',
            sidebar: false,
            activeMenu: '/taskAssignment/distributeTask'
          }
      },
      {
          path: 'videoPlay',
          name: 'videoPlay',
          component: () => import('@/views/safety/courseIndex/components/videoPlay.vue'),
          meta: {
            title: '视频',
            sidebar: false,
            activeMenu: '/taskAssignment/distributeTask'
          }
      },
      {
          path: 'courseSchedule',
          name: 'courseSchedule',
          component: () => import('@/views/safety/distribute/components/courseSchedule.vue'),
          meta: {
            title: '课程详情',
            sidebar: false,
            activeMenu: '/taskAssignment/distributeTask'
          }
      },
      {
          path: 'trainPlanDetail',
          name: 'trainPlanDetail',
          component: () => import('@/views/safety/planTrain/trainingPlan/trainPlanDetail.vue'),
          meta: {
            title: '培训计划详情',
            sidebar: false,
            activeMenu: '/taskAssignment/distributeTask'
          }
      },
      {
          path: 'examDetails',
          name: 'examDetails',
          component: () => import('@/views/safety/examPlan/examDetails.vue'),
          meta: {
            title: '试题详情',
            sidebar: false,
            activeMenu: '/taskAssignment/distributeTask'
          }
      },
      {
          path: 'answerInfo',
          name: 'answerInfo',
          component: () => import('@/views/safety/examRecord/answerInfo.vue'),
          meta: {
            title: '详情',
            sidebar: false,
            activeMenu: '/taskAssignment/distributeTask'
          }
      },
    ]
  },
  {
    path: '/trainingManagement',
    component: Layout,
    // name: 'studyManage',
    meta: {
      title: '培训管理',
      menuAuth: '/trainingManagement'
    },
    children: [
      { 
      path: 'trainingTemplate',
      component: EmptyLayout,
      name: 'trainingTemplate',
      meta: {
        title: '培训模板',
        menuAuth: '/trainingManagement/trainingTemplate'
      },
      children: [
        {
          path: '',
          name: 'trainingTemplate',
          component: () => import('@/views/safety/trainTemplate/trainingTemplate.vue'),
          meta: {
            title: '培训模板',
            sidebar: false,
            breadcrumb: false,
            activeMenu: '/trainingManagement/trainingTemplate'
          }
        },
        {
          path: 'addTemplate',
          component: () => import('@/views/safety/trainTemplate/components/addTemplate.vue'),
          name: 'addTemplate',
          meta: {
            title: '创建培训模板',
            sidebar: false,
            activeMenu: '/trainingManagement/trainingTemplate'
          }
        },
        {
          path: 'templateDetails',
          component: () => import('@/views/safety/trainTemplate/components/templateDetails.vue'),
          name: 'templateDetails',
          meta: {
            title: '培训模板详情',
            sidebar: false,
            activeMenu: '/trainingManagement/templateDetails'
          }
        },
        {
          path: 'seeTrainFile',
          name: 'seeTrainFile',
          component: () => import('@/views/safety/courseIndex/components/seeTrainFile.vue'),
          meta: {
            title: '课程详情',
            sidebar: false,
            activeMenu: '/trainingManagement/templateDetails'
          }
        },
      ]
      },
      { 
      path: 'trainingPlan',
      component: EmptyLayout,
      name: 'trainingPlan',
      meta: {
        title: '培训计划',
        menuAuth: '/trainingManagement/trainingPlan'
      },
      children: [
        {
          path: '',
          name: 'trainingPlan',
          component: () => import('@/views/safety/planTrain/trainingPlan.vue'),
          meta: {
            title: '培训计划',
            sidebar: false,
            breadcrumb: false,
            activeMenu: '/trainingManagement/trainingPlan'
          }
        },
        {
          path: 'addTrainPlan',
          name: 'addTrainPlan',
          component: () => import('@/views/safety/planTrain/components/addTrainPlan.vue'),
          meta: {
            title: '创建培训',
            sidebar: false,
            activeMenu: '/trainingManagement/trainingPlan'
          }
        },
        {
          path: 'trainPlanDetail',
          name: 'trainPlanDetail',
          component: () => import('@/views/safety/planTrain/trainingPlan/trainPlanDetail.vue'),
          meta: {
            title: '培训计划详情',
            sidebar: false,
            activeMenu: '/trainingManagement/trainingPlan'
          }
        },
        {
          path: 'conferencePlanDetail',
          name: 'conferencePlanDetail',
          component: () => import('@/views/safety/planTrain/trainingPlan/conferencePlanDetail.vue'),
          meta: {
            title: '会议计划详情',
            sidebar: false,
            activeMenu: '/trainingManagement/trainingPlan'
          }
        },
        {
          path: 'addConferencePlan',
          name: 'addConferencePlan',
          component: () => import('@/views/safety/planTrain/components/addConferencePlan.vue'),
          meta: {
            title: '创建培训',
            sidebar: false,
            activeMenu: '/trainingManagement/trainingPlan'
          }
        },
        {
          path: 'courseInfo',
          name: 'courseInfo',
          component: () => import('@/views/safety/courseIndex/components/courseInfo.vue'),
          meta: {
            title: '课程详情',
            sidebar: false,
            activeMenu: '/trainingManagement/trainingPlan'
          }
        },
        {
          path: 'seeFile',
          name: 'seeFile',
          component: () => import('@/views/safety/courseIndex/components/seeFile.vue'),
          meta: {
            title: '文件',
            sidebar: false,
            activeMenu: '/trainingManagement/trainingPlan'
          }
        },
        {
          path: 'videoPlay',
          name: 'videoPlay',
          component: () => import('@/views/safety/courseIndex/components/videoPlay.vue'),
          meta: {
            title: '视频',
            sidebar: false,
            activeMenu: '/trainingManagement/trainingPlan'
          }
        },
        ]
      },
      { 
      path: 'trainingTasks',
      component: EmptyLayout,
      name: 'trainingTasks',
      meta: {
        title: '培训任务',
        menuAuth: '/trainingManagement/trainingTasks'
      },
      children: [
        {
          path: '',
          name: 'trainingTasks',
          component: () => import('@/views/safety/taskTrain/trainingTasks.vue'),
          meta: {
            title: '培训任务',
            sidebar: false,
            breadcrumb: false,
            activeMenu: '/trainingManagement/trainingTasks'
          }
        },
        {
          path: 'addTrainlate',
          name: 'addTrainlate',
          component: () => import('@/views/safety/taskTrain/addTrainlate.vue'),
          meta: {
            title: '创建培训记录',
            sidebar: false,
            activeMenu: '/trainingManagement/trainingTasks'
          }
        },
        {
          path: 'addConference',
          name: 'addConference',
          component: () => import('@/views/safety/taskTrain/addConference.vue'),
          meta: {
            title: '创建会议记录',
            sidebar: false,
            activeMenu: '/trainingManagement/trainingTasks'
          }
        },
        {
          path: 'trainDetails',
          name: 'trainDetails',
          component: () => import('@/views/safety/taskTrain/trainDetails.vue'),
          meta: {
            title: '培训详情',
            sidebar: false,
            activeMenu: '/trainingManagement/trainingTasks'
          }
        },
        {
          path: 'answerInfo',
          name: 'answerInfo',
          component: () => import('@/views/safety/examRecord/answerInfo.vue'),
          meta: {
            title: '详情',
            sidebar: false,
            activeMenu: '/trainingManagement/trainingTasks'
          }
        },
        {
          path: 'courseInfo',
          name: 'courseInfo',
          component: () => import('@/views/safety/courseIndex/components/courseInfo.vue'),
          meta: {
            title: '课程详情',
            sidebar: false,
            activeMenu: '/trainingManagement/trainingTasks'
          }
        },
        {
          path: 'seeFile',
          name: 'seeFile',
          component: () => import('@/views/safety/courseIndex/components/seeFile.vue'),
          meta: {
            title: '文件',
            sidebar: false,
            activeMenu: '/trainingManagement/trainingTasks'
          }
        },
        {
          path: 'videoPlay',
          name: 'videoPlay',
          component: () => import('@/views/safety/courseIndex/components/videoPlay.vue'),
          meta: {
            title: '视频',
            sidebar: false,
            activeMenu: '/trainingManagement/trainingTasks'
          }
        },
      ]
      },
    ]
  },
  {
    path: '/examinationManagement',
    component: Layout,
    // name: 'studyManage',
    meta: {
      title: '考试管理',
      menuAuth: '/examinationManagement'
    },
    children: [
      { 
      path: 'questionBankManagement',
      component: EmptyLayout,
      name: 'questionBankManagement',
      meta: {
        title: '考题管理',
        menuAuth: '/examinationManagement/questionBankManagement'
      },
      children: [
        {
          path: '',
          name: 'questionBankManagement',
          component: () => import('@/views/safety/question/questionBankManagement.vue'),
          meta: {
            title: '考题管理',
            sidebar: false,
            breadcrumb: false,
            activeMenu: '/examinationManagement/questionBankManagement'
          }
        },
        {
          path: 'addQuestion',
          name: 'addQuestion',
          component: () => import('@/views/safety/question/addQuestion.vue'),
          meta: {
            title: '逐道录题',
            sidebar: false,
            activeMenu: '/examinationManagement/questionBankManagement'
          }
        },
      ]
      },
      { 
      path: 'examPlanIndex',
      component: EmptyLayout,
      name: 'examPlanIndex',
      meta: {
        title: '考试计划',
        menuAuth: '/examinationManagement/examPlanIndex'
      },
      children: [
        {
          path: '',
          name: 'examPlanIndex',
          component: () => import('@/views/safety/examPlan/examPlanIndex.vue'),
          meta: {
            title: '考试计划',
            sidebar: false,
            breadcrumb: false,
            activeMenu: '/examinationManagement/examPlanIndex'
          }
        },
        {
          path: 'addExam',
          name: 'addExam',
          component: () => import('@/views/safety/examPlan/addExam.vue'),
          meta: {
            title: '组卷',
            sidebar: false,
            activeMenu: '/examinationManagement/examPlanIndex'
          }
        },
        {
          path: 'examDetails',
          name: 'examDetails',
          component: () => import('@/views/safety/examPlan/examDetails.vue'),
          meta: {
            title: '试题详情',
            sidebar: false,
            activeMenu: '/examinationManagement/examPlanIndex'
          }
        },
      ]
      },
      { 
      path: 'examRecords',
      component: EmptyLayout,
      name: 'examRecords',
      meta: {
        title: '考试任务',
        menuAuth: '/examinationManagement/examRecords'
      },
      children: [
        {
          path: '',
          name: 'examRecords',
          component: () => import('@/views/safety/examRecord/examRecords.vue'),
          meta: {
            title: '考试任务',
            sidebar: false,
            breadcrumb: false,
            activeMenu: '/examinationManagement/examRecords'
          }
        },
        {
          path: 'questionsList',
          name: 'questionsList',
          component: () => import('@/views/safety/examRecord/questionsList.vue'),
          meta: {
            title: '在线考试',
            sidebar: false,
            activeMenu: '/examinationManagement/examRecords'
          }
        },
        {
          path: 'recordsDetails',
          name: 'recordsDetails',
          component: () => import('@/views/safety/examRecord/recordsDetails.vue'),
          meta: {
            title: '详情',
            sidebar: false,
            activeMenu: '/examinationManagement/examRecords'
          }
        },
        {
          path: 'answerInfo',
          name: 'answerInfo',
          component: () => import('@/views/safety/examRecord/answerInfo.vue'),
          meta: {
            title: '详情',
            sidebar: false,
            activeMenu: '/examinationManagement/examRecords'
          }
        },
        {
          path: 'addTask',
          name: 'addTask',
          component: () => import('@/views/safety/distribute/components/addTask.vue'),
          meta: {
            title: '创建学习任务',
            sidebar: false,
            activeMenu: '/examinationManagement/examRecords'
          }
        },
      ]
      },
      { 
      path: 'manualMarking',
      component: EmptyLayout,
      name: 'manualMarking',
      meta: {
        title: '人工评卷',
        menuAuth: '/examinationManagement/manualMarking'
      },
      children: [
        {
          path: '',
          name: 'manualMarking',
          component: () => import('@/views/safety/examRecord/manualMarking.vue'),
          meta: {
            title: '人工评卷',
            sidebar: false,
            breadcrumb: false,
            activeMenu: '/examinationManagement/manualMarking'
          }
        },
        {
          path: 'manualMarkDetails',
          name: 'manualMarkDetails',
          component: () => import('@/views/safety/examRecord/manualMarkDetails.vue'),
          meta: {
            title: '考试详情',
            sidebar: false,
            activeMenu: '/examinationManagement/manualMarking'
          }
        },
        {
          path: 'answeringDetails',
          name: 'answeringDetails',
          component: () => import('@/views/safety/examRecord/answeringDetails.vue'),
          meta: {
            title: '答题详情',
            sidebar: false,
            activeMenu: '/examinationManagement/manualMarking'
          }
        },
      ]
      }
    ]
  },
 {
    path: '/dataOverview',
    component: Layout,
    redirect: '/dataOverview/dataOverviewEcharts',
    name: 'dataOverview',
    meta: {
      title: '数据总览',
      menuAuth: '/dataOverview'
    },
    children: [
      {
        path: 'dataOverviewEcharts',
        component: EmptyLayout,
        redirect: { name: 'dataOverviewEcharts' },
        meta: {
          title: '数据总览',
          menuAuth: '/dataOverview/dataOverviewEcharts'
        },
        children: [
          {
            path: '',
            name: 'dataOverviewEcharts',
            component: () => import('@/views/safety/dataOverview/dataOverviewEcharts.vue'),
            meta: {
              title: '数据总览',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/dataOverview/dataOverviewEcharts'
            }
          }
        ]
      },
      {
        path: 'learningRate',
        component: EmptyLayout,
        redirect: { name: 'learningRate' },
        meta: {
          title: '学习任务完成率',
          menuAuth: '/dataOverview/learningRate'
        },
        children: [
          {
            path: '',
            name: 'learningRate',
            component: () => import('@/views/safety/dataOverview/learningRate.vue'),
            meta: {
              title: '学习任务完成率',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/dataOverview/learningRate'
            }
          },
          {
            path: 'learningDetails',
            name: 'learningDetails',
            component: () => import('@/views/safety/dataOverview/learningDetails.vue'),
            meta: {
              title: '任务详情',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/dataOverview/learningRate'
            }
          },
        ]
      }, {
        path: 'examRate',
        component: EmptyLayout,
        redirect: { name: 'examRate' },
        meta: {
          title: '考试通过率',
          menuAuth: '/dataOverview/examRate'
        },
        children: [
          {
            path: '',
            name: 'examRate',
            component: () => import('@/views/safety/dataOverview/examRate.vue'),
            meta: {
              title: '考试通过率',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/dataOverview/examRate'
            }
          },
          {
            path: 'examRateDetails',
            name: 'examRateDetails',
            component: () => import('@/views/safety/dataOverview/examRateDetails.vue'),
            meta: {
              title: '任务详情',
              sidebar: false,
              breadcrumb: false,
              activeMenu: '/dataOverview/examRate'
            }
          },
        ]
      },
    ],

  }
]